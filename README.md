# K8s-Envoy POC: Custom Service Mesh Implementation

This is a POC project for verifying the feasibility of using Kubernetes + Envoy to enhance access management and improve security through a **custom service mesh implementation**. Unlike typical Istio-based solutions, this project implements manual sidecar injection and static Envoy configurations to provide transparency and educational value about underlying service mesh mechanisms.

## Goals

Specifically, we'd like to achieve the following goals:
1. Restrict the list of URIs of a service to be accessed by other services.
2. If a pod was hacked, the hacker's intention trying to curl the local service should fail because the service's port is only open to envoy (enforced by iptables).
3. The access to internet by a service should be strictly controlled (as per configuration).
4. Demonstrate manual mTLS certificate management and rotation.
5. Show custom sidecar injection without relying on <PERSON><PERSON><PERSON>'s webhook server.

## Architecture

### Custom Implementation Approach
This project implements a **custom service mesh** without <PERSON><PERSON><PERSON> to provide better understanding of underlying mechanisms:

1. **Custom Sidecar Injection**: A custom Kubernetes admission webhook automatically injects Envoy sidecars into pods
2. **Static Envoy Configuration**: Envoy proxies use local mounted files for configuration instead of xDS APIs
3. **Manual Certificate Management**: Custom certificate generation and rotation system for mTLS
4. **Transparent Traffic Interception**: iptables rules redirect traffic through Envoy proxies

### System Components
1. **Application Services**:
   - Trading service (Python Flask)
   - Asset service (Python Flask)
2. **Apisix Gateway**: Entry point to the system
3. **Custom Sidecar Injector**: Kubernetes webhook for automatic Envoy injection
4. **Envoy Sidecars**: Custom-configured proxies with static configuration files
5. **Certificate Authority**: Custom CA for mTLS certificate management

### Setup
1. Use minikube to run k8s cluster locally
2. Deploy custom sidecar injection webhook
3. Generate and distribute mTLS certificates
4. Deploy services with automatic Envoy sidecar injection
5. Configure traffic policies through static Envoy configurations

### Key Features
- **mTLS Communication**: All service-to-service communication secured with mutual TLS
- **Traffic Filtering**: Lua filters for custom request/response processing
- **Domain Whitelisting**: SNI-based access control for external domains
- **Port Isolation**: iptables rules prevent direct access to application ports
- **Request Logging**: Comprehensive logging of all traffic flows

## Quick Start

### Prerequisites
- minikube
- kubectl
- Docker
- OpenSSL

### Deployment Steps

1. **Start minikube cluster**:
   ```bash
   minikube start
   ```

2. **Build Docker images**:
   ```bash
   ./scripts/build/build-images.sh

   # Load images into minikube
   minikube image load sidecar-injector:latest
   minikube image load custom-envoy:latest
   minikube image load trading-service:latest
   minikube image load asset-service:latest
   ```

3. **Deploy the system**:
   ```bash
   ./scripts/setup/deploy-services.sh
   ```

4. **Verify deployment**:
   ```bash
   kubectl get pods
   kubectl get services

   # Check sidecar injection
   kubectl describe pod -l app=trading-service
   ```

5. **Test the system**:
   ```bash
   ./scripts/test/test-system.sh
   ```

### Project Structure
```
k8s-envoy/
├── envoy/                    # Custom Envoy configurations and Docker image
├── sidecar-injector/         # Custom webhook for sidecar injection
├── k8s/                      # Kubernetes manifests
│   ├── envoy/               # Envoy ConfigMaps
│   ├── sidecar-injector/    # Webhook deployment
│   └── manifests/           # Service deployments
├── services/                 # Application services
│   ├── trading/             # Trading service (Python)
│   └── asset/               # Asset service (Python)
└── scripts/                 # Setup and build scripts
    ├── setup/               # Deployment scripts
    └── build/               # Image build scripts
```

### References
- [Envoy Proxy Documentation](https://www.envoyproxy.io/docs/envoy/latest/)
- [Envoy Lua Filter](https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/lua_filter.html)
- [Kubernetes Admission Controllers](https://kubernetes.io/docs/reference/access-authn-authz/admission-controllers/)