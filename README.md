This is a POC project for verifying the feasibility of using k8s + envoy to enhance access management and improve security. Specifically, we'd like to achieve the following goals:
1. Restrict the list of URIs of a service to be accessed by other services.
2. If a pod was hacked, the hacker's intention trying to curl the local service should fail because the service's port is only open to envoy (enforced by iptables).
3. The access to internet by a service should be strictly controlled (as per configuration).

## Setups
1. Use minikube to run k8s cluster locally.
2. Deploy envoy as a sidecar running in parallel along with service containers.
3. The system consists of:
    1. Application services: trading, asset(written in python for simplicity)
    2. Apisix Gateway acted as the entrypoint to the system.
4. Use <PERSON><PERSON><PERSON>'s control plane to manage the envoy proxies & configs.
5. Use Istio to setup & rotate mTLS certificates to enforce mTLS between services.
6. Proxy all outgoing traffic through envoy, and use https SNI to constrain access to whilite-listed domains and URIS.
7. Use https://istio.io/latest/docs/reference/config/networking/envoy-filter/ to write custom logics
8. https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/lua_filter.html