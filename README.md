# K8s-Envoy POC: Custom Service Mesh Implementation

This is a POC project for verifying the feasibility of using Kubernetes + Envoy to enhance access management and improve security through a **custom service mesh implementation**. Unlike typical Istio-based solutions, this project implements manual sidecar injection and static Envoy configurations to provide transparency and educational value about underlying service mesh mechanisms.

## Goals

Specifically, we'd like to achieve the following goals:
1. Restrict the list of URIs of a service to be accessed by other services.
2. If a pod was hacked, the hacker's intention trying to curl the local service should fail because the service's port is only open to envoy (enforced by iptables).
3. The access to internet by a service should be strictly controlled (as per configuration).
4. Demonstrate manual mTLS certificate management and rotation.
5. Show custom sidecar injection without relying on <PERSON><PERSON><PERSON>'s webhook server.

## Architecture

### Custom Implementation Approach
This project implements a **custom service mesh** without <PERSON><PERSON><PERSON> to provide better understanding of underlying mechanisms:

1. **Custom Sidecar Injection**: A custom Kubernetes admission webhook automatically injects Envoy sidecars into pods
2. **Static Envoy Configuration**: Envoy proxies use local mounted files for configuration instead of xDS APIs
3. **ServiceAccount JWT Authentication**: Uses Kubernetes ServiceAccount tokens for service authentication instead of mTLS
4. **Transparent Traffic Interception**: iptables rules redirect traffic through Envoy proxies

### Legacy System Architecture

This POC implements a **legacy system design** where all internal traffic flows through a central gateway:

#### Traffic Flow
- **Internal Communication**: `Service → Envoy Sidecar → Apisix Gateway → Target Service`
- **External Communication**: `Service → Envoy Sidecar → Internet` (bypasses Apisix)
- **Service Addressing**: Applications use `apisix-gateway.default.svc.cluster.local` for all internal calls
- **URI-Based Routing**: `/asset/*` routes to asset service, `/trading/*` routes to trading service

#### System Components
1. **Application Services**:
   - Trading service (Python Flask) - calls asset service via Apisix
   - Asset service (Python Flask) - provides data to trading service
2. **Apisix Gateway**: Central router for all internal service communication
3. **Custom Sidecar Injector**: Kubernetes webhook for automatic Envoy injection (excludes Apisix)
4. **Envoy Sidecars**: Custom-configured proxies with legacy routing logic
5. **ServiceAccount JWT Authentication**: Token-based service identity and access control

### Setup
1. Use minikube to run k8s cluster locally
2. Deploy custom sidecar injection webhook
3. Generate webhook TLS certificates (required for Kubernetes admission controllers)
4. Deploy services with automatic Envoy sidecar injection
5. Configure traffic policies through static Envoy configurations

### Key Features
- **Legacy System Routing**: All internal traffic routed through central Apisix gateway
- **JWT Authentication**: Service-to-service authentication using Kubernetes ServiceAccount tokens
- **URI-Level Access Control**: Fine-grained access policies enforced by Apisix
- **Traffic Filtering**: Lua filters for custom request/response processing and access control
- **Domain Whitelisting**: SNI-based access control for external domains
- **Port Isolation**: iptables rules prevent direct access to application ports
- **Request Logging**: Comprehensive logging of all traffic flows and access decisions

### Access Control Policies

The system implements fine-grained access control policies:

#### Trading Service Permissions
- ✅ **Allowed**: All `/trading/*` endpoints (self-access)
- ✅ **Allowed**: `/asset/prices` (read asset prices for trading)
- ✅ **Allowed**: `/asset/market-data` (read market data for analysis)
- ✅ **Allowed**: `/asset/assets/{symbol}` (read specific asset info)
- ❌ **Denied**: `/asset/assets` (cannot list all assets)
- ❌ **Denied**: `/asset/assets/*/price` PUT (cannot update prices)

#### Asset Service Permissions
- ✅ **Allowed**: All `/asset/*` endpoints (self-access)
- ✅ **Allowed**: `/trading/trades` GET (read trades for analysis)
- ✅ **Allowed**: `/trading/trades/symbol/*` (read trades by symbol)
- ✅ **Allowed**: `/trading/stats` (read trading statistics)
- ❌ **Denied**: `/trading/trades` POST (cannot create trades)

## Quick Start

### 1. Setup Kubernetes Cluster
```bash
minikube start

```

### 2. Deploy the System
```bash
# Deploy all components with legacy system architecture
./scripts/deploy/deploy-system.sh
```

This script will:
- Generate webhook TLS certificates
- Build and deploy service images
- Deploy Envoy configuration with legacy routing
- Deploy Apisix gateway with access control policies
- Deploy custom sidecar injector (excludes Apisix pods)
- Deploy trading and asset services with Envoy sidecars

### 3. Test the System
```bash
# Run comprehensive legacy system tests
./scripts/test/test-system.sh
```

This will test:
- Apisix gateway routing (`/asset/*`, `/trading/*`)
- Service-to-service communication via Apisix
- Access control policy enforcement
- External internet access (bypassing Apisix)
- Sidecar injection exclusion for Apisix

### Project Structure
```
k8s-envoy/
├── envoy/                    # Custom Envoy configurations and Docker image
├── sidecar-injector/         # Custom webhook for sidecar injection
├── k8s/                      # Kubernetes manifests
│   ├── envoy/               # Envoy ConfigMaps
│   ├── sidecar-injector/    # Webhook deployment
│   └── manifests/           # Service deployments
├── services/                 # Application services
│   ├── trading/             # Trading service (Python)
│   └── asset/               # Asset service (Python)
└── scripts/                 # Setup and build scripts
    ├── setup/               # Deployment scripts
    └── build/               # Image build scripts
```

### References
- [Envoy Proxy Documentation](https://www.envoyproxy.io/docs/envoy/latest/)
- [Envoy Lua Filter](https://www.envoyproxy.io/docs/envoy/latest/configuration/http/http_filters/lua_filter.html)
- [Kubernetes Admission Controllers](https://kubernetes.io/docs/reference/access-authn-authz/admission-controllers/)