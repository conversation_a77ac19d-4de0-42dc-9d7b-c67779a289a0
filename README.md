This is an POC project for verifying the the feasibility of using k8s + envoy to enhance access management and improve security, specifically:
1. 

## Setups
1. Use minikube to run k8s cluster locally.
2. Deploy envoy as a sidecar running parellelly along with service containers.
3. The system consists of:
    1. Application services: trading, asset, orderbook
    2. Envoy sidecar for each service
    3. Envoy proxy for external access
4. Use <PERSON><PERSON><PERSON>'s control plane to manage the envoy proxies & configs.
6. Use Istio to setup & rotate MTLS certificates to Enforce mTLS between services
