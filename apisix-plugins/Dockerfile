# Custom Apisix image with access-control plugin
FROM apache/apisix:3.13.0-debian

# Switch to root to copy files and set permissions
USER root

RUN apt update && apt install -y tcpdump net-tools curl procps

# Copy custom plugin
COPY access-control/ /usr/local/apisix/apisix/plugins/access-control/

# Ensure proper permissions and switch back to apisix user
RUN chown -R apisix:apisix /usr/local/apisix/apisix/plugins/access-control/ && \
    chmod -R 755 /usr/local/apisix/apisix/plugins/access-control/

# Switch back to apisix user for security
USER apisix

# The plugin will be automatically loaded by Apisix when referenced in configuration
