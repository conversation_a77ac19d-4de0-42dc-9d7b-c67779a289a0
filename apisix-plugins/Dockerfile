# Custom Apisix image with access-control plugin
FROM apache/apisix:3.13.0-debian

USER root

RUN apt update && apt install -y tcpdump net-tools curl sudo

# Copy custom plugin
COPY access-control/ /usr/local/apisix/apisix/plugins/access-control/

# Ensure proper permissions
RUN chmod -R 755 /usr/local/apisix/apisix/plugins/access-control/


# The plugin will be automatically loaded by Apisix when referenced in configuration
