# Custom Apisix Access Control Plugin

This directory contains a custom Apisix plugin that provides fine-grained access control based on service identity and URI patterns.

## Plugin Structure

```
apisix-plugins/
├── access-control/
│   ├── init.lua          # Main plugin logic
│   └── schema.lua        # Plugin configuration schema
├── Dockerfile            # Custom Apisix image with plugin
└── README.md            # This file
```

## Features

- **Service Identity Extraction**: Extracts service identity from JWT tokens
- **Policy Matrix Configuration**: Configurable access control policies per service
- **URI Pattern Matching**: Supports regex patterns for flexible URI matching
- **HTTP Method Filtering**: Optional method-specific access control
- **Detailed Logging**: Configurable logging levels for access decisions
- **JWT Token Forwarding**: Forwards original JWT and adds Apisix service token

## Configuration

The plugin is configured as a global plugin using a policy matrix that defines which services can access which URIs:

```yaml
global_plugins:
  access-control:
    policy_matrix:
      trading-service:
        - pattern: "/trading/.*"
          allowed: true
          description: "Full access to trading endpoints"
        - pattern: "/asset/prices"
          allowed: true
          description: "Read asset prices for trading decisions"
      asset-service:
        - pattern: "/asset/.*"
          allowed: true
          description: "Full access to asset endpoints"
      default:
        - pattern: "/.*"
          allowed: false
          description: "Deny all access by default"
    jwt_extraction_method: "simplified"
    default_deny: true
    log_level: "info"
```

## Policy Matrix Format

Each service can have multiple policy rules:

- **pattern**: URI pattern to match (supports regex)
- **allowed**: Boolean indicating if access is allowed
- **method**: Optional HTTP method (GET, POST, PUT, DELETE, etc.)
- **description**: Human-readable description of the rule

## JWT Extraction Methods

- **simplified**: Basic pattern matching in JWT token (demo mode)
- **payload**: Proper JWT payload decoding (TODO: implement)
- **header**: Extract from JWT header (TODO: implement)

## Building and Deployment

The custom Apisix image is built automatically during deployment:

```bash
# Build custom image
docker build -t apisix-custom:latest apisix-plugins/

# Load into minikube
minikube image load apisix-custom:latest
```

## Benefits Over Inline Lua

1. **Modularity**: Plugin code is separate from configuration
2. **Global Application**: Applies to all routes automatically as a global plugin
3. **Centralized Policy**: Single policy matrix for all access control decisions
4. **Maintainability**: Easier to update and version
5. **Configuration**: Policy matrix is externalized and configurable
6. **Schema Validation**: Built-in configuration validation
7. **Performance**: Better caching and optimization by Apisix

## Migration from Inline Lua

The plugin replaces the previous `serverless-pre-function` approach:

**Before (Inline Lua)**:
- Lua code embedded in each route configuration
- Policy matrix duplicated across routes
- Difficult to maintain and update

**After (Global Custom Plugin)**:
- Clean separation of code and configuration
- Single centralized policy matrix for all routes
- Automatic application to all routes
- Proper schema validation and error handling
