-- Custom Access Control Plugin for Apisix
-- Provides fine-grained access control based on service identity and URI patterns

local core = require("apisix.core")
local plugin_name = "access-control"
local external_domain = 'be.wf.com'

local schema = {
    type = "object",
    properties = {
        policy_matrix = {
            type = "object",
            description = "Access control policy matrix defining service permissions",
            patternProperties = {
                ["^[a-zA-Z0-9_-]+$"] = {
                    type = "array",
                    items = {
                        type = "object",
                        properties = {
                            pattern = {
                                type = "string",
                                description = "URI pattern to match (regex supported)"
                            },
                            allowed = {
                                type = "boolean",
                                description = "Whether access is allowed"
                            },
                            method = {
                                type = "string",
                                description = "HTTP method (optional, applies to all if not specified)",
                                enum = {"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"}
                            },
                            description = {
                                type = "string",
                                description = "Human-readable description of the policy rule"
                            }
                        },
                        required = {"pattern", "allowed"}
                    }
                }
            },
            additionalProperties = false
        },
        jwt_extraction_method = {
            type = "string",
            description = "Method to extract service identity from JW<PERSON>",
            enum = {"header", "payload", "simplified"},
            default = "simplified"
        },
        default_deny = {
            type = "boolean",
            description = "Whether to deny access by default if no policy matches",
            default = true
        },
        log_level = {
            type = "string",
            description = "Logging level for access control decisions",
            enum = {"debug", "info", "warn", "error"},
            default = "info"
        }
    },
    required = {"policy_matrix"}
}


local _M = {
    version = 0.1,
    priority = 2000,  -- High priority to run early in the request lifecycle
    name = plugin_name,
    schema = schema,
}

-- Extract service identity from JWT token
local function extract_service_identity(jwt_token, extraction_method)
    if not jwt_token then
        return "unknown-service"
    end
    
    -- Remove "Bearer " prefix if present
    local token = jwt_token:gsub("^Bearer%s+", "")
    
    if extraction_method == "simplified" then
        -- Simplified extraction for demo purposes
        -- In production, you would properly decode the JWT
        if string.find(token, "trading") then
            return "trading-service"
        elseif string.find(token, "asset") then
            return "asset-service"
        else
            return "unknown-service"
        end
    elseif extraction_method == "payload" then
        -- TODO: Implement proper JWT payload decoding
        -- This would involve base64 decoding the payload and parsing JSON
        return "unknown-service"
    else
        -- Header-based extraction (default)
        return "unknown-service"
    end
end

-- Check if access is allowed based on policy matrix
local function check_access_permission(service_name, uri, method, policy_matrix)
    local service_policies = policy_matrix[service_name]
    if not service_policies then
        -- Fallback to default policy
        service_policies = policy_matrix["default"] or {}
    end
    
    -- Check each policy rule in order
    for _, policy in ipairs(service_policies) do
        local pattern_match = string.match(uri, policy.pattern)
        local method_match = true
        
        -- Check method if specified in policy
        if policy.method then
            method_match = (method == policy.method)
        end
        
        if pattern_match and method_match then
            return policy.allowed, policy.description or "Policy matched"
        end
    end
    
    -- No policy matched
    return false, "No matching policy found"
end

-- Main access control logic
function _M.access(conf, ctx)
    local uri = ctx.var.uri
    local host = ctx.var.host

    core.log.info("Access control for " .. host .. uri)
    if host == external_domain then
        -- for traffics from external domain, we've got other more strict access control in place handled by other plugins
        -- but we dismiss it here since it's not relevant to POC project
    else
        local method = ctx.var.request_method
        local auth_header = core.request.header(ctx, "X-ServiceAccount-JWT")
        
        -- Extract service identity
        local service_name = extract_service_identity(auth_header, conf.jwt_extraction_method)
        -- check permission
        local allowed, reason = check_access_permission(service_name, uri, method, conf.policy_matrix)
        if not allowed then
            local error_msg = string.format(
                "Access denied for service '%s' to %s %s - %s",
                service_name, method, uri, reason
            )
            
            -- Log the denial
            if conf.log_level == "debug" or conf.log_level == "info" then
                core.log.warn(error_msg)
            end
            
            -- Return 403 Forbidden with detailed error
            return 403, {
                error = error_msg,
                service = service_name,
                uri = uri,
                method = method,
                reason = reason
            }
        end

        -- Log successful access
        if conf.log_level == "debug" or conf.log_level == "info" then
            local success_msg = string.format(
                "Access granted for service '%s' to %s %s",
                service_name, method, uri
            )
            core.log.info(success_msg)
        end
    end

    -- Forward the ServiceAccount JWT that Envoy already added
    -- Envoy sidecar already reads the ServiceAccount token and adds it as Authorization header
    local existing_auth = core.request.header(ctx, "Authorization")
    if existing_auth then
        core.log.info("Forwarding existing Authorization header from Envoy: " .. (existing_auth:sub(1, 20) .. "..."))
        -- Header is already set by Envoy, just log for confirmation
    else
        core.log.warn("No Authorization header found from Envoy sidecar")
        -- Fallback: try to read ServiceAccount token directly (requires root permissions)
        local token_path = "/var/run/secrets/kubernetes.io/serviceaccount/token"
        local apisix_jwt, err = core.io.get_file(token_path)
        if apisix_jwt then
            local clean_token = apisix_jwt:gsub("%s+", "")
            core.log.info("Setting Authorization header with directly read ServiceAccount JWT")
            core.request.set_header(ctx, "Authorization", "Bearer " .. clean_token)
        else
            core.log.error("Failed to read ServiceAccount token: " .. (err or "unknown error"))
        end
    end
end

return _M
