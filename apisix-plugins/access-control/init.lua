-- Custom Access Control Plugin for Apisix
-- Provides fine-grained access control based on service identity and URI patterns

local core = require("apisix.core")
local plugin_name = "access-control"

local schema = require("apisix.plugins.access-control.schema")

local _M = {
    version = 0.1,
    priority = 2000,  -- High priority to run early in the request lifecycle
    name = plugin_name,
    schema = schema,
}

-- Extract service identity from JWT token
local function extract_service_identity(jwt_token, extraction_method)
    if not jwt_token then
        return "unknown-service"
    end
    
    -- Remove "Bearer " prefix if present
    local token = jwt_token:gsub("^Bearer%s+", "")
    
    if extraction_method == "simplified" then
        -- Simplified extraction for demo purposes
        -- In production, you would properly decode the JWT
        if string.find(token, "trading") then
            return "trading-service"
        elseif string.find(token, "asset") then
            return "asset-service"
        else
            return "unknown-service"
        end
    elseif extraction_method == "payload" then
        -- TODO: Implement proper JWT payload decoding
        -- This would involve base64 decoding the payload and parsing JSON
        return "unknown-service"
    else
        -- Header-based extraction (default)
        return "unknown-service"
    end
end

-- Check if access is allowed based on policy matrix
local function check_access_permission(service_name, uri, method, policy_matrix)
    local service_policies = policy_matrix[service_name]
    if not service_policies then
        -- Fallback to default policy
        service_policies = policy_matrix["default"] or {}
    end
    
    -- Check each policy rule in order
    for _, policy in ipairs(service_policies) do
        local pattern_match = string.match(uri, policy.pattern)
        local method_match = true
        
        -- Check method if specified in policy
        if policy.method then
            method_match = (method == policy.method)
        end
        
        if pattern_match and method_match then
            return policy.allowed, policy.description or "Policy matched"
        end
    end
    
    -- No policy matched
    return false, "No matching policy found"
end

-- Main access control logic
function _M.access(conf, ctx)
    local uri = ctx.var.uri
    local method = ctx.var.request_method
    local auth_header = core.request.header(ctx, "X-ServiceAccount-JWT")
    
    -- Extract service identity
    local service_name = extract_service_identity(auth_header, conf.jwt_extraction_method)

    if not allowed then
        local error_msg = string.format(
            "Access denied for service '%s' to %s %s - %s",
            service_name, method, uri, reason
        )
        
        -- Log the denial
        if conf.log_level == "debug" or conf.log_level == "info" then
            core.log.warn(error_msg)
        end
        
        -- Return 403 Forbidden with detailed error
        return 403, {
            error = error_msg,
            service = service_name,
            uri = uri,
            method = method,
            reason = reason
        }
    end

    -- Log successful access
    if conf.log_level == "debug" or conf.log_level == "info" then
        local success_msg = string.format(
            "Access granted for service '%s' to %s %s",
            service_name, method, uri
        )
        core.log.info(success_msg)
    end

    
    -- Add Apisix's own ServiceAccount JWT for authentication
    local apisix_jwt_file = io.open("/var/run/secrets/kubernetes.io/serviceaccount/token", "r")
    if apisix_jwt_file then
        local apisix_jwt = apisix_jwt_file:read("*all")
        apisix_jwt_file:close()
        if apisix_jwt and apisix_jwt ~= "" then
            core.request.set_header(ctx, "X-ServiceAccount-JWT", "Bearer " .. apisix_jwt:gsub("%s+", ""))
        end
    end
    
    -- Log successful access if debug enabled
    if conf.log_level == "debug" then
        core.log.info(string.format(
            "Access granted for service '%s' to %s %s",
            service_name, method, uri
        ))
    end
end

return _M
