-- Schema definition for the access-control plugin
local core = require("apisix.core")

local schema = {
    type = "object",
    properties = {
        policy_matrix = {
            type = "object",
            description = "Access control policy matrix defining service permissions",
            patternProperties = {
                ["^[a-zA-Z0-9_-]+$"] = {
                    type = "array",
                    items = {
                        type = "object",
                        properties = {
                            pattern = {
                                type = "string",
                                description = "URI pattern to match (regex supported)"
                            },
                            allowed = {
                                type = "boolean",
                                description = "Whether access is allowed"
                            },
                            method = {
                                type = "string",
                                description = "HTTP method (optional, applies to all if not specified)",
                                enum = {"GET", "POST", "PUT", "DELETE", "PATCH", "HEAD", "OPTIONS"}
                            },
                            description = {
                                type = "string",
                                description = "Human-readable description of the policy rule"
                            }
                        },
                        required = {"pattern", "allowed"}
                    }
                }
            },
            additionalProperties = false
        },
        jwt_extraction_method = {
            type = "string",
            description = "Method to extract service identity from JWT",
            enum = {"header", "payload", "simplified"},
            default = "simplified"
        },
        default_deny = {
            type = "boolean",
            description = "Whether to deny access by default if no policy matches",
            default = true
        },
        log_level = {
            type = "string",
            description = "Logging level for access control decisions",
            enum = {"debug", "info", "warn", "error"},
            default = "info"
        }
    },
    required = {"policy_matrix"}
}

return schema
