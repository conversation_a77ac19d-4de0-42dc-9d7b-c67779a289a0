# Getting Started with k8s-envoy POC

This guide will help you set up and run the k8s-envoy proof of concept project.

## Prerequisites

- Docker
- minikube
- kubectl
- curl
- jq (optional, for pretty JSON output)

## Quick Start

### 1. Setup minikube
```bash
./scripts/setup/setup-minikube.sh
```

### 2. Install Istio
```bash
./scripts/setup/install-istio.sh
```

### 3. Build service images
```bash
./scripts/setup/build-images.sh
```

### 4. Deploy services
```bash
./scripts/setup/deploy-services.sh
```

### 5. Test the system
```bash
./scripts/test/test-system.sh
```

## Architecture Overview

The system consists of:

- **Trading Service**: Python Flask API for trading operations
- **Asset Service**: Python Flask API for asset management
- **Apisix Gateway**: Entry point for external traffic
- **Istio Service Mesh**: Provides mTLS, traffic management, and security policies
- **Envoy Sidecars**: Automatically injected by <PERSON><PERSON><PERSON> for each service

## Security Features Implemented

1. **mTLS Communication**: All service-to-service communication is encrypted
2. **Authorization Policies**: Restrict which services can communicate with each other
3. **URI-based Access Control**: Limit which endpoints each service can access
4. **Network Isolation**: Services can only communicate through Envoy sidecars

## API Endpoints

### Through Apisix Gateway (External Access)

- `GET /api/assets/health` - Asset service health check
- `GET /api/assets/assets` - List all assets
- `GET /api/assets/assets/{symbol}` - Get specific asset
- `POST /api/assets/assets` - Create new asset
- `PUT /api/assets/assets/{symbol}` - Update asset
- `GET /api/assets/stats` - Asset service statistics

- `GET /api/trading/health` - Trading service health check
- `GET /api/trading/trades` - List all trades
- `POST /api/trading/trades` - Create new trade
- `GET /api/trading/trades/{id}` - Get specific trade
- `GET /api/trading/stats` - Trading service statistics

### Example API Calls

```bash
# Get all assets
curl http://localhost:8080/api/assets/assets

# Create a trade
curl -X POST http://localhost:8080/api/trading/trades \
  -H "Content-Type: application/json" \
  -d '{"symbol":"AAPL","quantity":10,"price":150.0,"side":"buy"}'

# Get trading stats
curl http://localhost:8080/api/trading/stats
```

## Monitoring and Observability

### Access Istio Dashboard (Kiali)
```bash
kubectl port-forward -n istio-system svc/kiali 20001:20001
# Open http://localhost:20001
```

### Access Grafana
```bash
kubectl port-forward -n istio-system svc/grafana 3000:3000
# Open http://localhost:3000
```

### View Service Logs
```bash
# Trading service logs
kubectl logs -l app=trading-service -c trading-service

# Asset service logs
kubectl logs -l app=asset-service -c asset-service

# Envoy sidecar logs
kubectl logs -l app=trading-service -c istio-proxy
```

## Troubleshooting

### Check Pod Status
```bash
kubectl get pods
kubectl describe pod <pod-name>
```

### Check Service Connectivity
```bash
# Test internal service communication
kubectl exec -it deployment/trading-service -c trading-service -- curl http://asset-service:8080/health
```

### Verify mTLS
```bash
# Check if mTLS is working
kubectl exec -it deployment/trading-service -c istio-proxy -- openssl s_client -connect asset-service:8080
```

### Check Istio Configuration
```bash
# Verify Istio injection
kubectl get pods -o jsonpath='{.items[*].spec.containers[*].name}'

# Check Istio policies
kubectl get peerauthentication
kubectl get authorizationpolicy
```

## Cleanup

To remove all deployed resources:
```bash
./scripts/setup/cleanup.sh
```

To completely reset the environment:
```bash
./scripts/setup/cleanup.sh
istioctl uninstall --purge
minikube delete
```

## Next Steps

1. **Network Policies**: Add Kubernetes NetworkPolicies for additional network isolation
2. **Monitoring**: Set up Prometheus and Grafana for comprehensive monitoring
3. **Logging**: Implement centralized logging with ELK stack
4. **Security Scanning**: Add container image scanning and vulnerability assessment
5. **Load Testing**: Perform load testing to validate performance under stress
