# Project Structure

This document outlines the directory structure and organization of the k8s-envoy POC project.

## Directory Layout

```
k8s-envoy/
├── README.md                    # Project overview and goals
├── docs/                        # Documentation
│   ├── PROJECT_STRUCTURE.md     # This file
│   └── architecture/            # Architecture diagrams and docs
├── services/                    # Application services
│   ├── trading/                 # Trading service (Python)
│   │   ├── app/                 # Application code
│   │   ├── Dockerfile           # Container image definition
│   │   └── requirements.txt     # Python dependencies
│   └── asset/                   # Asset service (Python)
│       ├── app/                 # Application code
│       ├── Dockerfile           # Container image definition
│       └── requirements.txt     # Python dependencies
├── k8s/                         # Kubernetes configurations
│   ├── manifests/               # K8s resource manifests
│   │   ├── trading/             # Trading service manifests
│   │   ├── asset/               # Asset service manifests
│   │   └── apisix/              # Apisix gateway manifests
│   ├── istio/                   # Istio service mesh configs
│   │   ├── policies/            # Security policies, PeerAuthentication
│   │   └── gateways/            # VirtualServices, DestinationRules
│   └── apisix/                  # Apisix specific configurations
└── scripts/                     # Automation scripts
    ├── setup/                   # Setup and deployment scripts
    └── test/                    # Testing scripts
```

## Component Overview

### Services
- **Trading Service**: Python-based service handling trading operations
- **Asset Service**: Python-based service managing asset information

### Infrastructure
- **Kubernetes**: Container orchestration platform
- **Istio**: Service mesh for traffic management, security, and observability
- **Envoy**: Proxy sidecar (managed by Istio)
- **Apisix**: API Gateway as system entry point

### Security Features
- mTLS between services (managed by Istio)
- Network policies to restrict service-to-service communication
- URI-based access control
- Sidecar proxy isolation

## Next Steps
1. Implement Python services with basic REST APIs
2. Create Kubernetes deployment manifests
3. Configure Istio for service mesh
4. Set up Apisix gateway
5. Create deployment and testing scripts
