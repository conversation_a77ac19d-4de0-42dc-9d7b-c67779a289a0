FROM envoyproxy/envoy:v1.28-latest

# Install iptables for traffic redirection
USER root
RUN apt-get update && \
    apt-get install -y iptables && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Create envoy user and group
RUN groupadd -r envoy && useradd -r -g envoy envoy

# Create directories for configuration and certificates
RUN mkdir -p /etc/envoy /etc/ssl/envoy /var/log/envoy && \
    chown -R envoy:envoy /etc/envoy /etc/ssl/envoy /var/log/envoy

# Copy default configuration
COPY configs/envoy-template.yaml /etc/envoy/envoy.yaml

# Set proper permissions
RUN chmod 644 /etc/envoy/envoy.yaml

# Switch to envoy user
USER envoy

# Expose Envoy ports
EXPOSE 15000 15001 15006

# Start Envoy with the configuration
CMD ["/usr/local/bin/envoy", "-c", "/etc/envoy/envoy.yaml", "--service-cluster", "envoy-sidecar"]
