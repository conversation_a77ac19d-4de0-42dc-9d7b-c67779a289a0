admin:
  address:
    socket_address:
      address: 0.0.0.0
      port_value: 15000

static_resources:
  listeners:
  # Inbound listener
  - name: inbound_listener
    address:
      socket_address:
        address: 0.0.0.0
        port_value: 15006
    filter_chains:
    - filters:
      - name: envoy.filters.network.http_connection_manager
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
          stat_prefix: inbound_http
          codec_type: AUTO
          route_config:
            name: inbound_route
            virtual_hosts:
            - name: inbound_service
              domains: ["*"]
              routes:
              - match:
                  prefix: "/"
                route:
                  cluster: local_service
          http_filters:
          - name: envoy.filters.http.router
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
      transport_socket:
        name: envoy.transport_sockets.tls
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.DownstreamTlsContext
          common_tls_context:
            tls_certificates:
            - certificate_chain:
                filename: "/etc/ssl/envoy/tls.crt"
              private_key:
                filename: "/etc/ssl/envoy/tls.key"
            validation_context:
              trusted_ca:
                filename: "/etc/ssl/envoy/ca.crt"
          require_client_certificate: true

  # Outbound listener
  - name: outbound_listener
    address:
      socket_address:
        address: 0.0.0.0
        port_value: 15001
    filter_chains:
    - filters:
      - name: envoy.filters.network.http_connection_manager
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
          stat_prefix: outbound_http
          codec_type: AUTO
          route_config:
            name: outbound_route
            virtual_hosts:
            - name: services
              domains: ["*"]
              routes:
              - match:
                  prefix: "/"
                route:
                  cluster: passthrough
          http_filters:
          - name: envoy.filters.http.router
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router

  clusters:
  # Local service cluster
  - name: local_service
    connect_timeout: 0.25s
    type: STATIC
    lb_policy: ROUND_ROBIN
    load_assignment:
      cluster_name: local_service
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: 127.0.0.1
                port_value: 8080

  # Passthrough cluster for outbound traffic
  - name: passthrough
    connect_timeout: 5s
    type: ORIGINAL_DST
    lb_policy: CLUSTER_PROVIDED
