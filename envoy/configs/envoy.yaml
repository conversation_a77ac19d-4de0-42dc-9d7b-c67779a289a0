admin:
  address:
    socket_address:
      address: 0.0.0.0
      port_value: 15000

static_resources:
  listeners:
  # Inbound listener - receives traffic from other services
  - name: inbound_listener
    address:
      socket_address:
        address: 0.0.0.0
        port_value: 15006
    filter_chains:
    - filters:
      - name: envoy.filters.network.http_connection_manager
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
          stat_prefix: inbound_http
          codec_type: AUTO
          route_config:
            name: inbound_route
            virtual_hosts:
            - name: inbound_service
              domains: ["*"]
              routes:
              - match:
                  prefix: "/"
                route:
                  cluster: local_service
          http_filters:
          - name: envoy.filters.http.lua
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.lua.v3.Lua
              inline_code: |
                function envoy_on_request(request_handle)
                  -- Add custom request logging
                  request_handle:headers():add("x-envoy-inbound", "true")
                  request_handle:logInfo("Inbound request: " .. request_handle:headers():get(":path"))
                end
                
                function envoy_on_response(response_handle)
                  -- Add custom response headers
                  response_handle:headers():add("x-envoy-processed", "true")
                end
          - name: envoy.filters.http.router
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router
      transport_socket:
        name: envoy.transport_sockets.tls
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.DownstreamTlsContext
          common_tls_context:
            tls_certificates:
            - certificate_chain:
                filename: "/etc/ssl/envoy/tls.crt"
              private_key:
                filename: "/etc/ssl/envoy/tls.key"
            validation_context:
              trusted_ca:
                filename: "/etc/ssl/envoy/ca.crt"
          require_client_certificate: true

  # Outbound listener - intercepts traffic to other services
  - name: outbound_listener
    address:
      socket_address:
        address: 0.0.0.0
        port_value: 15001
    filter_chains:
    - filters:
      - name: envoy.filters.network.http_connection_manager
        typed_config:
          "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
          stat_prefix: outbound_http
          codec_type: AUTO
          route_config:
            name: outbound_route
            virtual_hosts:
            - name: trading_service
              domains: ["trading-service", "trading-service.default.svc.cluster.local"]
              routes:
              - match:
                  prefix: "/"
                route:
                  cluster: trading_service
            - name: asset_service
              domains: ["asset-service", "asset-service.default.svc.cluster.local"]
              routes:
              - match:
                  prefix: "/"
                route:
                  cluster: asset_service
            - name: external_services
              domains: ["*"]
              routes:
              - match:
                  prefix: "/"
                route:
                  cluster: external_cluster
          http_filters:
          - name: envoy.filters.http.lua
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.lua.v3.Lua
              inline_code: |
                function envoy_on_request(request_handle)
                  local host = request_handle:headers():get(":authority")
                  local path = request_handle:headers():get(":path")
                  
                  -- Log outbound requests
                  request_handle:logInfo("Outbound request to: " .. host .. path)
                  
                  -- Add custom headers
                  request_handle:headers():add("x-envoy-outbound", "true")
                  request_handle:headers():add("x-envoy-source", os.getenv("POD_NAME") or "unknown")
                  
                  -- Implement whitelist logic for external domains
                  local allowed_domains = {
                    ["trading-service"] = true,
                    ["trading-service.default.svc.cluster.local"] = true,
                    ["asset-service"] = true,
                    ["asset-service.default.svc.cluster.local"] = true,
                    ["httpbin.org"] = true,  -- Example allowed external domain
                    ["api.github.com"] = true  -- Example allowed external domain
                  }
                  
                  if not allowed_domains[host] then
                    request_handle:logWarn("Blocked request to unauthorized domain: " .. host)
                    request_handle:respond(
                      {[":status"] = "403"},
                      "Access to domain " .. host .. " is not allowed"
                    )
                  end
                end
          - name: envoy.filters.http.router
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router

  clusters:
  # Local service cluster (the main application container)
  - name: local_service
    connect_timeout: 0.25s
    type: STATIC
    lb_policy: ROUND_ROBIN
    load_assignment:
      cluster_name: local_service
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: 127.0.0.1
                port_value: 8080

  # Trading service cluster
  - name: trading_service
    connect_timeout: 0.25s
    type: STRICT_DNS
    lb_policy: ROUND_ROBIN
    load_assignment:
      cluster_name: trading_service
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: trading-service.default.svc.cluster.local
                port_value: 15006  # Connect to Envoy inbound port
    transport_socket:
      name: envoy.transport_sockets.tls
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
        common_tls_context:
          tls_certificates:
          - certificate_chain:
              filename: "/etc/ssl/envoy/tls.crt"
            private_key:
              filename: "/etc/ssl/envoy/tls.key"
          validation_context:
            trusted_ca:
              filename: "/etc/ssl/envoy/ca.crt"

  # Asset service cluster
  - name: asset_service
    connect_timeout: 0.25s
    type: STRICT_DNS
    lb_policy: ROUND_ROBIN
    load_assignment:
      cluster_name: asset_service
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: asset-service.default.svc.cluster.local
                port_value: 15006  # Connect to Envoy inbound port
    transport_socket:
      name: envoy.transport_sockets.tls
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
        common_tls_context:
          tls_certificates:
          - certificate_chain:
              filename: "/etc/ssl/envoy/tls.crt"
            private_key:
              filename: "/etc/ssl/envoy/tls.key"
          validation_context:
            trusted_ca:
              filename: "/etc/ssl/envoy/ca.crt"

  # External services cluster (for internet access)
  - name: external_cluster
    connect_timeout: 5s
    type: LOGICAL_DNS
    lb_policy: ROUND_ROBIN
    load_assignment:
      cluster_name: external_cluster
      endpoints:
      - lb_endpoints:
        - endpoint:
            address:
              socket_address:
                address: httpbin.org
                port_value: 443
    transport_socket:
      name: envoy.transport_sockets.tls
      typed_config:
        "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
        sni: httpbin.org
