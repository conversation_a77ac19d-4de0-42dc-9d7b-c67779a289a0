# This singleton service account aggregates reader permissions for the revisions in a given cluster
# ATM this is a singleton per cluster with Istio installed, and is not revisioned. It maybe should be,
# as otherwise compromising the token for this SA would give you access to *every* installed revision.
# Should be used for remote secret creation.
apiVersion: v1
kind: ServiceAccount
  {{- if .Values.global.imagePullSecrets }}
imagePullSecrets:
  {{- range .Values.global.imagePullSecrets }}
  - name: {{ . }}
    {{- end }}
    {{- end }}
metadata:
  name: istio-reader-service-account
  namespace: {{ .Values.global.istioNamespace }}
  labels:
    app: istio-reader
    release: {{ .Release.Name }}
    app.kubernetes.io/name: "istio-reader"
    {{- include "istio.labels" . | nindent 4 }}
