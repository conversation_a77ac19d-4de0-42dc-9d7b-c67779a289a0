# "_internal_defaults_do_not_set" is a workaround for <PERSON><PERSON> limitations. Users should NOT set "._internal_defaults_do_not_set" explicitly, but rather directly set the fields internally.
# For instance, instead of `--set _internal_defaults_do_not_set.foo=bar``, just set `--set foo=bar`.
_internal_defaults_do_not_set:
  global:

    # ImagePullSecrets for control plane ServiceAccount, list of secrets in the same namespace
    # to use for pulling any images in pods that reference this ServiceAccount.
    # Must be set for any cluster configured with private docker registry.
    imagePullSecrets: []

    # Used to locate istiod.
    istioNamespace: istio-system
  base:
    # A list of CRDs to exclude. Requires `enableCRDTemplates` to be true.
    # Example: `excludedCRDs: ["envoyfilters.networking.istio.io"]`.
    # Note: when installing with `istioctl`, `enableIstioConfigCRDs=false` must also be set.
    excludedCRDs: []
    # Helm (as of V3) does not support upgrading CRDs, because it is not universally
    # safe for them to support this.
    # Istio as a project enforces certain backwards-compat guarantees that allow us
    # to safely upgrade CRDs in spite of this, so we default to self-managing CRDs
    # as standard K8S resources in Helm, and disable <PERSON><PERSON>'s CRD management. See also:
    # https://helm.sh/docs/chart_best_practices/custom_resource_definitions/#method-2-separate-charts
    enableCRDTemplates: true

    # Validation webhook configuration url
    # For example: https://$remotePilotAddress:15017/validate
    validationURL: ""
    # Validation webhook caBundle value. Useful when running pilot with a well known cert
    validationCABundle: ""

    # For istioctl usage to disable istio config crds in base
    enableIstioConfigCRDs: true

  defaultRevision: "default"
  experimental:
    stableValidationPolicy: false
