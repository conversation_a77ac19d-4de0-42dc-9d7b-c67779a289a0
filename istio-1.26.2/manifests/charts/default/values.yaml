# "_internal_defaults_do_not_set" is a workaround for Helm limitations. Users should NOT set "._internal_defaults_do_not_set" explicitly, but rather directly set the fields internally.
# For instance, instead of `--set _internal_defaults_do_not_set.foo=bar``, just set `--set foo=bar`.
_internal_defaults_do_not_set:
  global:
    # Used to locate istiod.
    istioNamespace: "istio-system"

  base:
    # Validation webhook configuration url
    # For example: https://$remotePilotAddress:15017/validate
    validationURL: ""

  istiodRemote:
    # Sidecar injector mutating webhook configuration url
    # For example: https://$remotePilotAddress:15017/inject
    injectionURL: ""

  # Revision is set as 'version' label and part of the resource names when installing multiple control planes.
  revision: ""

  sidecarInjectorWebhook:
    # This enables injection of sidecar in all namespaces,
    enableNamespacesByDefault: false

