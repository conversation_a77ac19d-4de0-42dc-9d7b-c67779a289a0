{{- if and (.Values.autoscaling.enabled) (eq .Values.kind "Deployment") }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ include "gateway.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: {{ include "gateway.name" . }}
    {{- include "istio.labels" . | nindent 4}}
    {{- include "gateway.labels" . | nindent 4 }}
  annotations:
    {{- .Values.annotations | toYaml | nindent 4 }}
spec:
  scaleTargetRef:
    apiVersion: apps/v1
    kind: {{ .Values.kind | default "Deployment" }}
    name: {{ include "gateway.name" . }}
  minReplicas: {{ .Values.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.maxReplicas }}
  metrics:
    {{- if .Values.autoscaling.targetCPUUtilizationPercentage }}
    - type: Resource
      resource:
        name: cpu
        target:
          averageUtilization: {{ .Values.autoscaling.targetCPUUtilizationPercentage }}
          type: Utilization
    {{- end }}
    {{- if .Values.autoscaling.targetMemoryUtilizationPercentage }}
    - type: Resource
      resource:
        name: memory
        target:
          averageUtilization: {{ .Values.autoscaling.targetMemoryUtilizationPercentage }}
          type: Utilization
    {{- end }}
  {{- if .Values.autoscaling.autoscaleBehavior }}
  behavior: {{ toYaml .Values.autoscaling.autoscaleBehavior | nindent 4 }}
  {{- end }}
{{- end }}
