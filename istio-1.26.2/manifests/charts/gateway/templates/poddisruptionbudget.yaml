{{- if .Values.podDisruptionBudget }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ include "gateway.name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: {{ include "gateway.name" . }}
    {{- include "istio.labels" . | nindent 4}}
    {{- include "gateway.labels" . | nindent 4}}
spec:
  selector:
    matchLabels:
  {{- include "gateway.selectorLabels" . | nindent 6 }}
  {{- with .Values.podDisruptionBudget }}
    {{- toYaml . | nindent 2 }}
  {{- end }}
{{- end }}
