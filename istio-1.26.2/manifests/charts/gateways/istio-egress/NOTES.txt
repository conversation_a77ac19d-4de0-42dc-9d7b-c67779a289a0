
Changes:
- separate namespace allows:
-- easier reconfig of just the gateway
-- TLS secrets and domain name management is isolated, for better security
-- simplified configuration
-- multiple versions of the ingress can be used, to minize upgrade risks

- the new chart uses the default namespace service account, and doesn't require
additional RBAC permissions.

- simplified label structure. Label change is not supported on upgrade.

- for 'internal load balancer' you should deploy a separate gateway, in a different
namespace.

All ingress gateway have a "app:ingressgateway" label, used to identify it as an
ingress, and an "istio: ingressgateway$SUFFIX" label of Gateway selection.

The Gateways use "istio: ingressgateway$SUFFIX" selectors.


# Multiple gateway versions



# Using different pilot versions



# Migration from istio-system

Istio 1.0 includes the gateways in istio-system. Since the external IP is associated
with the Service and bound to the namespace, it is recommended to:

1. Install the new gateway in a new namespace.
2. Copy any TLS certificate to the new namespace, and configure the domains.
3. Checking the new gateway work - for example by overriding the IP in /etc/hosts
4. Modify the DNS server to add the A record of the new namespace
5. Check traffic
6. Delete the A record corresponding to the gateway in istio-system
7. Upgrade istio-system, disabling the ingressgateway
8. Delete the domain TLS certs from istio-system.

If using certmanager, all Certificate and associated configs must be moved as well.
