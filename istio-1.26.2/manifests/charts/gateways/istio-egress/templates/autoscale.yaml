{{ $gateway := index .Values "gateways" "istio-egressgateway" }}
{{- if and $gateway.autoscaleEnabled $gateway.autoscaleMin $gateway.autoscaleMax }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{ $gateway.name }}
  namespace: {{ .Release.Namespace }}
  labels:
{{ $gateway.labels | toYaml | indent 4 }}
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" | quote }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "EgressGateways"
    app.kubernetes.io/name: "istio-egressgateway"
    {{- include "istio.labels" . | nindent 4 }}
spec:
  maxReplicas: {{ $gateway.autoscaleMax }}
  minReplicas: {{ $gateway.autoscaleMin }}
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ $gateway.name }}
  metrics:
  - type: Resource
    resource:
      name: cpu
      target:
        type: Utilization
        averageUtilization: {{ $gateway.cpu.targetAverageUtilization }}
---
{{- end }}
