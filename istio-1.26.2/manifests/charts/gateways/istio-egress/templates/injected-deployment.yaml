{{- $gateway := index .Values "gateways" "istio-egressgateway" }}
{{- if ne $gateway.injectionTemplate "" }}
{{/* This provides a minimal gateway, ready to be injected.
     Any settings from values.gateways should be here - these are options specific to the gateway.
     Global settings, like the image, various env vars and volumes, etc will be injected.
     The normal Deployment is not suitable for this, as the original pod spec will override the injection template. */}}
apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ $gateway.name | default "istio-egressgateway" }}
  namespace: {{ .Release.Namespace }}
  labels:
{{ $gateway.labels | toYaml | indent 4 }}
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" | quote }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "EgressGateways"
    app.kubernetes.io/name: "istio-egressgateway"
    {{- include "istio.labels" . | nindent 4 }}
spec:
{{- if not $gateway.autoscaleEnabled }}
{{- if $gateway.replicaCount }}
  replicas: {{ $gateway.replicaCount }}
{{- end }}
{{- end }}
  selector:
    matchLabels:
{{ $gateway.labels | toYaml | indent 6 }}
  strategy:
    rollingUpdate:
      maxSurge: {{ $gateway.rollingMaxSurge }}
      maxUnavailable: {{ $gateway.rollingMaxUnavailable }}
  template:
    metadata:
      labels:
{{ $gateway.labels | toYaml | indent 8 }}
{{- if eq .Release.Namespace "istio-system"}}
        heritage: Tiller
        release: istio
        chart: gateways
{{- end }}
        install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
        operator.istio.io/component: "EgressGateways"
        sidecar.istio.io/inject: "true"
        {{- with .Values.revision }}
        istio.io/rev: {{ . }}
        {{- end }}
        service.istio.io/canonical-name: {{ $gateway.name }}
        service.istio.io/canonical-revision: {{ index $gateway.labels "app.kubernetes.io/version" | default (index $gateway.labels "version") | default .Values.revision | default "latest" | quote }}
        app.kubernetes.io/name: "istio-egressgateway"
        {{- include "istio.labels" . | nindent 8 }}
      annotations:
        {{- if .Values.meshConfig.enablePrometheusMerge }}
        prometheus.io/port: "15020"
        prometheus.io/scrape: "true"
        prometheus.io/path: "/stats/prometheus"
        {{- end }}
        sidecar.istio.io/inject: "true"
        inject.istio.io/templates: "{{ $gateway.injectionTemplate }}"
{{- if $gateway.podAnnotations }}
{{ toYaml $gateway.podAnnotations | indent 8 }}
{{ end }}
    spec:
{{- if not $gateway.runAsRoot }}
      securityContext:
{{- if not (eq (coalesce .Values.platform .Values.global.platform) "openshift") }}
        runAsUser: 1337
        runAsGroup: 1337
{{- end }}
        runAsNonRoot: true
{{- end }}
      serviceAccountName: {{ $gateway.name | default "istio-egressgateway" }}-service-account
{{- if .Values.global.priorityClassName }}
      priorityClassName: "{{ .Values.global.priorityClassName }}"
{{- end }}
      containers:
        - name: istio-proxy
          image: auto
{{- if .Values.global.imagePullPolicy }}
          imagePullPolicy: {{ .Values.global.imagePullPolicy }}
{{- end }}
          ports:
            {{- range $key, $val := $gateway.ports }}
            - containerPort: {{ $val.targetPort | default $val.port }}
              protocol: {{ $val.protocol | default "TCP" }}
            {{- end }}
            - containerPort: 15090
              protocol: TCP
              name: http-envoy-prom
        {{- if not $gateway.runAsRoot }}
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            privileged: false
            readOnlyRootFilesystem: true
        {{- end }}
          resources:
{{- if $gateway.resources }}
{{ toYaml $gateway.resources | indent 12 }}
{{- else }}
{{ toYaml .Values.global.defaultResources | indent 12 }}
{{- end }}
          env:
          {{- if not $gateway.runAsRoot }}
          - name: ISTIO_META_UNPRIVILEGED_POD
            value: "true"
          {{- end }}
          {{- range $key, $val := $gateway.env }}
          - name: {{ $key }}
            value: {{ $val | quote }}
          {{- end }}
          volumeMounts:
          {{- range $gateway.secretVolumes }}
          - name: {{ .name }}
            mountPath: {{ .mountPath | quote }}
            readOnly: true
          {{- end }}
          {{- range $gateway.configVolumes }}
          {{- if .mountPath }}
          - name: {{ .name }}
            mountPath: {{ .mountPath | quote }}
            readOnly: true
          {{- end }}
          {{- end }}
{{- if $gateway.additionalContainers }}
{{ toYaml $gateway.additionalContainers | indent 8 }}
{{- end }}
      volumes:
      {{- range $gateway.secretVolumes }}
      - name: {{ .name }}
        secret:
          secretName: {{ .secretName | quote }}
          optional: true
      {{- end }}
      {{- range $gateway.configVolumes }}
      - name: {{ .name }}
        configMap:
          name: {{ .configMapName | quote }}
          optional: true
      {{- end }}
      affinity:
{{ include "nodeaffinity" (dict "global" .Values.global "nodeSelector" $gateway.nodeSelector) | trim | indent 8 }}
      {{- include "podAntiAffinity" $gateway | indent 6 }}
{{- if $gateway.tolerations }}
      tolerations:
{{ toYaml $gateway.tolerations | indent 6 }}
{{- else if .Values.global.defaultTolerations }}
      tolerations:
{{ toYaml .Values.global.defaultTolerations | indent 6 }}
{{- end }}
{{- end }}
