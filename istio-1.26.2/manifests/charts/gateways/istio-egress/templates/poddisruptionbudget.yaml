{{- if .Values.global.defaultPodDisruptionBudget.enabled }}
{{ $gateway := index .Values "gateways" "istio-egressgateway" }}
apiVersion: policy/v1
kind: PodDisruptionBudget
metadata:
  name: {{ $gateway.name }}
  namespace: {{ .Release.Namespace }}
  labels:
{{ $gateway.labels | toYaml | trim | indent 4 }}
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" | quote }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "EgressGateways"
    app.kubernetes.io/name: "istio-egressgateway"
    {{- include "istio.labels" . | nindent 4 }}
spec:
  minAvailable: 1
  selector:
    matchLabels:
{{ $gateway.labels | toYaml | trim | indent 6 }}
{{- end }}
