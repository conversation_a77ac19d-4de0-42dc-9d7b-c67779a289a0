{{ $gateway := index .Values "gateways" "istio-egressgateway" }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: {{ $gateway.name }}-sds
  namespace: {{ .Release.Namespace }}
  labels:
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" | quote }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "EgressGateways"
    app.kubernetes.io/name: "istio-egressgateway"
    {{- include "istio.labels" . | nindent 4 }}
rules:
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "watch", "list"]
---
