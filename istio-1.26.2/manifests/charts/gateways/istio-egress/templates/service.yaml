{{ $gateway := index .Values "gateways" "istio-egressgateway" }}
{{- if not $gateway.customService }}
apiVersion: v1
kind: Service
metadata:
  name: {{ $gateway.name }}
  namespace: {{ .Release.Namespace }}
  annotations:
    {{- range $key, $val := $gateway.serviceAnnotations }}
    {{ $key }}: {{ $val | quote }}
    {{- end }}
  labels:
{{ $gateway.labels | toYaml | indent 4 }}
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" | quote }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "EgressGateways"
    app.kubernetes.io/name: "istio-egressgateway"
    {{- include "istio.labels" . | nindent 4 }}
spec:
{{- if $gateway.loadBalancerIP }}
  loadBalancerIP: "{{ $gateway.loadBalancerIP }}"
{{- end }}
{{- if $gateway.loadBalancerSourceRanges }}
  loadBalancerSourceRanges:
{{ toYaml $gateway.loadBalancerSourceRanges | indent 4 }}
{{- end }}
{{- if $gateway.externalTrafficPolicy }}
  externalTrafficPolicy: {{$gateway.externalTrafficPolicy }}
{{- end }}
  type: {{ $gateway.type }}
  selector:
{{ $gateway.labels | toYaml | indent 4 }}
  ports:

    {{- range $key, $val := $gateway.ports }}
    -
      {{- range $pkey, $pval := $val }}
      {{ $pkey}}: {{ $pval }}
      {{- end }}
    {{- end }}

  {{ range $app := $gateway.egressPorts }}
    -
      port: {{ $app.port }}
      name: {{ $app.name }}
  {{- end }}
{{- if $gateway.ipFamilyPolicy }}
  ipFamilyPolicy: {{ $gateway.ipFamilyPolicy }}
{{- end }}
{{- if $gateway.ipFamilies }}
  ipFamilies:
{{- range $gateway.ipFamilies }}
  - {{ . }}
{{- end }}
{{- end }}
---
{{ end }}
