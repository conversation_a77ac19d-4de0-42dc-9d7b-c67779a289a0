
Changes:
- separate namespace allows:
-- easier reconfig of just the gateway
-- TLS secrets and domain name management is isolated, for better security
-- simplified configuration
-- multiple versions of the ingress can be used, to minimize upgrade risks

- the new chart uses the default namespace service account, and doesn't require
additional RBAC permissions.

- simplified label and chart structure.
- ability to run a pilot dedicated for the gateway, isolated from the main pilot. This is more robust, safer on upgrades
and allows a bit more flexibility.
- the dedicated pilot-per-ingress is required if the gateway needs to support k8s-style ingress.

# Port and basic host configuration

In order to configure the Service object, the install/upgrade needs to provide a list of all ports.
In the past, this was done when installing/upgrading full istio, and involved some duplication - ports configured
both in upgrade, Gateway and VirtualService.

The new Ingress chart uses a 'values.yaml' (see user-example-ingress), which auto-generates Service ports,
Gateways and basic VirtualService. It is still possible to only configure the ports in Service, and do manual
config for the rest.

All internal services ( telemetry, pilot debug ports, mesh expansion ) can now be configured via the new mechanism.

# Migration from istio-system

Istio 1.0 includes the gateways in istio-system. Since the external IP is associated
with the Service and bound to the namespace, it is recommended to:

1. Install the new gateway in a new namespace.
2. Copy any TLS certificate to the new namespace, and configure the domains.
3. Checking the new gateway work - for example by overriding the IP in /etc/hosts
4. Modify the DNS server to add the A record of the new namespace
5. Check traffic
6. Delete the A record corresponding to the gateway in istio-system
7. Upgrade istio-system, disabling the ingressgateway
8. Delete the domain TLS certs from istio-system.

If using certmanager, all Certificate and associated configs must be moved as well.
