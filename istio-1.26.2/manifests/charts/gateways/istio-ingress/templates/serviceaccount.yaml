{{ $gateway := index .Values "gateways" "istio-ingressgateway" }}
apiVersion: v1
kind: ServiceAccount
{{- if .Values.global.imagePullSecrets }}
imagePullSecrets:
{{- range .Values.global.imagePullSecrets }}
  - name: {{ . }}
{{- end }}
{{- end }}
metadata:
  name: {{ $gateway.name }}-service-account
  namespace: {{ .Release.Namespace }}
  labels:
{{ $gateway.labels | toYaml | trim | indent 4 }}
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" | quote }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "IngressGateways"
    app.kubernetes.io/name: "istio-ingressgateway"
    {{- include "istio.labels" . | nindent 4 }}
  {{- with $gateway.serviceAccount.annotations }}
  annotations:
    {{- toYaml . | nindent 4 }}
  {{- end }}
