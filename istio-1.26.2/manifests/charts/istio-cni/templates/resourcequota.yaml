{{- if .Values.resourceQuotas.enabled }}
apiVersion: v1
kind: ResourceQuota
metadata:
  name: {{ template "name" . }}-resource-quota
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: {{ template "name" . }}
    {{- include "istio.labels" . | nindent 4 }}
spec:
  hard:
    pods: {{ .Values.resourceQuotas.pods | quote }}
  scopeSelector:
    matchExpressions:
    - operator: In
      scopeName: PriorityClass
      values:
      - system-node-critical
{{- end }}
