apiVersion: v1
kind: ServiceAccount
{{- if .Values.global.imagePullSecrets }}
imagePullSecrets:
{{- range .Values.global.imagePullSecrets }}
  - name: {{ . }}
{{- end }}
{{- end }}
metadata:
  name: {{ template "name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app: {{ template "name" . }}
    release: {{ .Release.Name }}
    istio.io/rev: {{ .Values.revision | default "default" }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "Cni"
    app.kubernetes.io/name: {{ template "name" . }}
    {{- include "istio.labels" . | nindent 4 }}
