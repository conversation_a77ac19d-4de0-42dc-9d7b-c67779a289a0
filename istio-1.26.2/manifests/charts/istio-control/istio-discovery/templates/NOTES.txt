"istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}" successfully installed!

To learn more about the release, try:
  $ helm status {{ .Release.Name }} -n {{ .Release.Namespace }}
  $ helm get all {{ .Release.Name }} -n {{ .Release.Namespace }}

Next steps:
{{- $profile := default "" .Values.profile }}
{{- if (eq $profile "ambient") }}
  * Get started with ambient: https://istio.io/latest/docs/ops/ambient/getting-started/
  * Review ambient's architecture: https://istio.io/latest/docs/ops/ambient/architecture/
{{- else }}
  * Deploy a Gateway: https://istio.io/latest/docs/setup/additional-setup/gateway/
  * Try out our tasks to get started on common configurations:
    * https://istio.io/latest/docs/tasks/traffic-management
    * https://istio.io/latest/docs/tasks/security/
    * https://istio.io/latest/docs/tasks/policy-enforcement/
{{- end }}
  * Review the list of actively supported releases, CVE publications and our hardening guide:
    * https://istio.io/latest/docs/releases/supported-releases/
    * https://istio.io/latest/news/security/
    * https://istio.io/latest/docs/ops/best-practices/security/

For further documentation see https://istio.io website

{{-
  $deps := dict
    "global.outboundTrafficPolicy" "meshConfig.outboundTrafficPolicy"
    "global.certificates" "meshConfig.certificates"
    "global.localityLbSetting" "meshConfig.localityLbSetting"
    "global.policyCheckFailOpen" "meshConfig.policyCheckFailOpen"
    "global.enableTracing" "meshConfig.enableTracing"
    "global.proxy.accessLogFormat" "meshConfig.accessLogFormat"
    "global.proxy.accessLogFile" "meshConfig.accessLogFile"
    "global.proxy.concurrency" "meshConfig.defaultConfig.concurrency"
    "global.proxy.envoyAccessLogService" "meshConfig.defaultConfig.envoyAccessLogService"
    "global.proxy.envoyAccessLogService.enabled" "meshConfig.enableEnvoyAccessLogService"
    "global.proxy.envoyMetricsService" "meshConfig.defaultConfig.envoyMetricsService"
    "global.proxy.protocolDetectionTimeout" "meshConfig.protocolDetectionTimeout"
    "global.proxy.holdApplicationUntilProxyStarts" "meshConfig.defaultConfig.holdApplicationUntilProxyStarts"
    "pilot.ingress" "meshConfig.ingressService, meshConfig.ingressControllerMode, and meshConfig.ingressClass"
    "global.mtls.enabled" "the PeerAuthentication resource"
    "global.mtls.auto" "meshConfig.enableAutoMtls"
    "global.tracer.lightstep.address" "meshConfig.defaultConfig.tracing.lightstep.address"
    "global.tracer.lightstep.accessToken" "meshConfig.defaultConfig.tracing.lightstep.accessToken"
    "global.tracer.zipkin.address" "meshConfig.defaultConfig.tracing.zipkin.address"
    "global.tracer.datadog.address" "meshConfig.defaultConfig.tracing.datadog.address"
    "global.meshExpansion.enabled" "Gateway and other Istio networking resources, such as in samples/multicluster/"
    "istiocoredns.enabled" "the in-proxy DNS capturing (ISTIO_META_DNS_CAPTURE)"
}}
{{- range $dep, $replace := $deps }}
{{- /* Complex logic to turn the string above into a null-safe traversal like ((.Values.global).certificates */}}
{{- $res := tpl (print "{{" (repeat (split "." $dep | len) "(")  ".Values." (replace "." ")." $dep) ")}}") $}}
{{- if not (eq $res "")}}
WARNING: {{$dep|quote}} is deprecated; use {{$replace|quote}} instead.
{{- end }}
{{- end }}
{{-
  $failDeps := dict
    "telemetry.v2.prometheus.configOverride"
    "telemetry.v2.stackdriver.configOverride"
    "telemetry.v2.stackdriver.disableOutbound"
    "telemetry.v2.stackdriver.outboundAccessLogging"
    "global.tracer.stackdriver.debug" "meshConfig.defaultConfig.tracing.stackdriver.debug"
    "global.tracer.stackdriver.maxNumberOfAttributes" "meshConfig.defaultConfig.tracing.stackdriver.maxNumberOfAttributes"
    "global.tracer.stackdriver.maxNumberOfAnnotations" "meshConfig.defaultConfig.tracing.stackdriver.maxNumberOfAnnotations"
    "global.tracer.stackdriver.maxNumberOfMessageEvents" "meshConfig.defaultConfig.tracing.stackdriver.maxNumberOfMessageEvents"
    "meshConfig.defaultConfig.tracing.stackdriver.debug" "Istio supported tracers"
    "meshConfig.defaultConfig.tracing.stackdriver.maxNumberOfAttributes" "Istio supported tracers"
    "meshConfig.defaultConfig.tracing.stackdriver.maxNumberOfAnnotations" "Istio supported tracers"
    "meshConfig.defaultConfig.tracing.stackdriver.maxNumberOfMessageEvents" "Istio supported tracers"
}}
{{- range $dep, $replace := $failDeps }}
{{- /* Complex logic to turn the string above into a null-safe traversal like ((.Values.global).certificates */}}
{{- $res := tpl (print "{{" (repeat (split "." $dep | len) "(")  ".Values." (replace "." ")." $dep) ")}}") $}}
{{- if not (eq $res "")}}
{{fail (print $dep " is removed")}}
{{- end }}
{{- end }}
{{- if eq $.Values.global.pilotCertProvider "kubernetes" }}
{{- fail "pilotCertProvider=kubernetes is not supported" }}
{{- end }}