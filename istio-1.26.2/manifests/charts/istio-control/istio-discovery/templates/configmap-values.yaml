apiVersion: v1
kind: ConfigMap
metadata:
  name: values{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
  namespace: {{ .Release.Namespace }}
  annotations:
    kubernetes.io/description: This ConfigMap contains the Helm values used during chart rendering. This ConfigMap is rendered for debugging purposes and external tooling; modifying these values has no effect.
  labels:
    istio.io/rev: {{ .Values.revision | default "default" | quote }}
    install.operator.istio.io/owning-resource: {{ .Values.ownerName | default "unknown" }}
    operator.istio.io/component: "Pilot"
    release: {{ .Release.Name }}
    app.kubernetes.io/name: "istiod"
    {{- include "istio.labels" . | nindent 4 }}
data:
  original-values: |-
{{ .Values._original | toPrettyJson | indent 4 }}
{{- $_ := unset $.Values "_original" }}
  merged-values: |-
{{ .Values | toPrettyJson | indent 4 }}
