# This file is only used for remote `istiod` installs.
{{- if .Values.istiodRemote.enabled }}
# if the remotePilotAddress is an IP addr
{{- if regexMatch "^([0-9]*\\.){3}[0-9]*$" .Values.global.remotePilotAddress }}
apiVersion: v1
kind: Endpoints
metadata:
  name: istiod{{- if .Values.revision }}-{{ .Values.revision}}{{- end }}
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: "istiod"
    {{- include "istio.labels" . | nindent 4 }}
subsets:
- addresses:
  - ip: {{ .Values.global.remotePilotAddress }}
  ports:
  - port: 15012
    name: tcp-istiod
    protocol: TCP
  - port: 15017
    name: tcp-webhook
    protocol: TCP
---
{{- end }}
{{- end }}
