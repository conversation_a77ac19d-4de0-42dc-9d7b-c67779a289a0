# Created if this is not a remote istiod, OR if it is and is also a config cluster
{{- if or (and .Values.istiodRemote.enabled .Values.global.configCluster) (not .Values.istiodRemote.enabled) }}
apiVersion: rbac.authorization.k8s.io/v1
kind: Role
metadata:
  name: istiod{{- if not (eq .Values.revision "")}}-{{ .Values.revision }}{{- end }}
  namespace: {{ .Values.global.istioNamespace }}
  labels:
    app: istiod
    release: {{ .Release.Name }}
    app.kubernetes.io/name: "istiod"
    {{- include "istio.labels" . | nindent 4 }}
rules:
# permissions to verify the webhook is ready and rejecting
# invalid config. We use --server-dry-run so no config is persisted.
- apiGroups: ["networking.istio.io"]
  verbs: ["create"]
  resources: ["gateways"]

# For storing CA secret
- apiGroups: [""]
  resources: ["secrets"]
  # TODO lock this down to istio-ca-cert if not using the DNS cert mesh config
  verbs: ["create", "get", "watch", "list", "update", "delete"]

# For status controller, so it can delete the distribution report configmap
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["delete"]

# For gateway deployment controller
- apiGroups: ["coordination.k8s.io"]
  resources: ["leases"]
  verbs: ["get", "update", "patch", "create"]
{{- end }}
