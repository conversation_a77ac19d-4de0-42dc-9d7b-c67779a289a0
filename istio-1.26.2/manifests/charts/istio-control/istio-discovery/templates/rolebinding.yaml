# Created if this is not a remote istiod, OR if it is and is also a config cluster
{{- if or (and .Values.istiodRemote.enabled .Values.global.configCluster) (not .Values.istiodRemote.enabled) }}
apiVersion: rbac.authorization.k8s.io/v1
kind: RoleBinding
metadata:
  name: istiod{{- if not (eq .Values.revision "")}}-{{ .Values.revision }}{{- end }}
  namespace: {{ .Values.global.istioNamespace }}
  labels:
    app: istiod
    release: {{ .Release.Name }}
    app.kubernetes.io/name: "istiod"
    {{- include "istio.labels" . | nindent 4 }}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: Role
  name: istiod{{- if not (eq .Values.revision "")}}-{{ .Values.revision }}{{- end }}
subjects:
  - kind: ServiceAccount
    name: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
    namespace: {{ .Values.global.istioNamespace }}
{{- end }}
