# Created if this is not a remote istiod, OR if it is and is also a config cluster
{{- if or (and .Values.istiodRemote.enabled .Values.global.configCluster) (not .Values.istiodRemote.enabled) }}
apiVersion: v1
kind: ServiceAccount
  {{- if .Values.global.imagePullSecrets }}
imagePullSecrets:
  {{- range .Values.global.imagePullSecrets }}
  - name: {{ . }}
  {{- end }}
  {{- end }}
metadata:
  name: istiod{{- if not (eq .Values.revision "") }}-{{ .Values.revision }}{{- end }}
  namespace: {{ .Values.global.istioNamespace }}
  labels:
    app: istiod
    release: {{ .Release.Name }}
    app.kubernetes.io/name: "istiod"
    {{- include "istio.labels" . | nindent 4 }}
  {{- if .Values.serviceAccountAnnotations }}
  annotations:
{{- toYaml .Values.serviceAccountAnnotations | nindent 4 }}
  {{- end }}
{{- end }}
---
