# "_internal_defaults_do_not_set" is a workaround for Helm limitations. Users should NOT set "._internal_defaults_do_not_set" explicitly, but rather directly set the fields internally.
# For instance, instead of `--set _internal_defaults_do_not_set.foo=bar``, just set `--set foo=bar`.
_internal_defaults_do_not_set:
  autoscaleEnabled: true
  autoscaleMin: 1
  autoscaleMax: 5
  autoscaleBehavior: {}
  replicaCount: 1
  rollingMaxSurge: 100%
  rollingMaxUnavailable: 25%

  hub: ""
  tag: ""
  variant: ""

  # Can be a full hub/image:tag
  image: pilot
  traceSampling: 1.0

  # Resources for a small pilot install
  resources:
    requests:
      cpu: 500m
      memory: 2048Mi

  # Set to `type: RuntimeDefault` to use the default profile if available.
  seccompProfile: {}

  # Whether to use an existing CNI installation
  cni:
    enabled: false
    provider: default

  # Additional container arguments
  extraContainerArgs: []

  env: {}

  envVarFrom: []

  # Settings related to the untaint controller
  # This controller will remove `cni.istio.io/not-ready` from nodes when the istio-cni pod becomes ready
  # It should be noted that cluster operator/owner is responsible for having the taint set by their infrastructure provider when new nodes are added to the cluster; the untaint controller does not taint nodes
  taint:
    # Controls whether or not the untaint controller is active
    enabled: false
    # What namespace the untaint controller should watch for istio-cni pods. This is only required when istio-cni is running in a different namespace than istiod
    namespace: ""

  affinity: {}

  tolerations: []

  cpu:
    targetAverageUtilization: 80
  memory: {}
    # targetAverageUtilization: 80

  # Additional volumeMounts to the istiod container
  volumeMounts: []

  # Additional volumes to the istiod pod
  volumes: []

  # Inject initContainers into the istiod pod
  initContainers: []

  nodeSelector: {}
  podAnnotations: {}
  serviceAnnotations: {}
  serviceAccountAnnotations: {}
  sidecarInjectorWebhookAnnotations: {}

  topologySpreadConstraints: []

  # You can use jwksResolverExtraRootCA to provide a root certificate
  # in PEM format. This will then be trusted by pilot when resolving
  # JWKS URIs.
  jwksResolverExtraRootCA: ""

  # The following is used to limit how long a sidecar can be connected
  # to a pilot. It balances out load across pilot instances at the cost of
  # increasing system churn.
  keepaliveMaxServerConnectionAge: 30m

  # Additional labels to apply to the deployment.
  deploymentLabels: {}

  ## Mesh config settings

  # Install the mesh config map, generated from values.yaml.
  # If false, pilot wil use default values (by default) or user-supplied values.
  configMap: true

  # Additional labels to apply on the pod level for monitoring and logging configuration.
  podLabels: {}

  # Setup how istiod Service is configured. See https://kubernetes.io/docs/concepts/services-networking/dual-stack/#services
  ipFamilyPolicy: ""
  ipFamilies: []

  # Ambient mode only.
  # Set this if you install ztunnel to a different namespace from `istiod`.
  # If set, `istiod` will allow connections from trusted node proxy ztunnels
  # in the provided namespace.
  # If unset, `istiod` will assume the trusted node proxy ztunnel resides
  # in the same namespace as itself.
  trustedZtunnelNamespace: ""
  # Set this if you install ztunnel with a name different from the default.
  trustedZtunnelName: ""

  sidecarInjectorWebhook:
    # You can use the field called alwaysInjectSelector and neverInjectSelector which will always inject the sidecar or
    # always skip the injection on pods that match that label selector, regardless of the global policy.
    # See https://istio.io/docs/setup/kubernetes/additional-setup/sidecar-injection/#more-control-adding-exceptions
    neverInjectSelector: []
    alwaysInjectSelector: []

    # injectedAnnotations are additional annotations that will be added to the pod spec after injection
    # This is primarily to support PSP annotations. For example, if you defined a PSP with the annotations:
    #
    # annotations:
    #   apparmor.security.beta.kubernetes.io/allowedProfileNames: runtime/default
    #   apparmor.security.beta.kubernetes.io/defaultProfileName: runtime/default
    #
    # The PSP controller would add corresponding annotations to the pod spec for each container. However, this happens before
    # the inject adds additional containers, so we must specify them explicitly here. With the above example, we could specify:
    # injectedAnnotations:
    #   container.apparmor.security.beta.kubernetes.io/istio-init: runtime/default
    #   container.apparmor.security.beta.kubernetes.io/istio-proxy: runtime/default
    injectedAnnotations: {}

    # This enables injection of sidecar in all namespaces,
    # with the exception of namespaces with "istio-injection:disabled" annotation
    # Only one environment should have this enabled.
    enableNamespacesByDefault: false

    # Mutations that occur after the sidecar injector are not handled by default, as the Istio sidecar injector is only run
    # once. For example, an OPA sidecar injected after the Istio sidecar will not have it's liveness/readiness probes rewritten.
    # Setting this to `IfNeeded` will result in the sidecar injector being run again if additional mutations occur.
    reinvocationPolicy: Never

    rewriteAppHTTPProbe: true

    # Templates defines a set of custom injection templates that can be used. For example, defining:
    #
    # templates:
    #   hello: |
    #     metadata:
    #       labels:
    #         hello: world
    #
    # Then starting a pod with the `inject.istio.io/templates: hello` annotation, will result in the pod
    # being injected with the hello=world labels.
    # This is intended for advanced configuration only; most users should use the built in template
    templates: {}

    # Default templates specifies a set of default templates that are used in sidecar injection.
    # By default, a template `sidecar` is always provided, which contains the template of default sidecar.
    # To inject other additional templates, define it using the `templates` option, and add it to
    # the default templates list.
    # For example:
    #
    # templates:
    #   hello: |
    #     metadata:
    #       labels:
    #         hello: world
    #
    # defaultTemplates: ["sidecar", "hello"]
    defaultTemplates: []
  istiodRemote:
    # If `true`, indicates that this cluster/install should consume a "remote istiod" installation,
    # and istiod itself will NOT be installed in this cluster - only the support resources necessary
    # to utilize a remote instance.
    enabled: false
    # Sidecar injector mutating webhook configuration clientConfig.url value.
    # For example: https://$remotePilotAddress:15017/inject
    # The host should not refer to a service running in the cluster; use a service reference by specifying
    # the clientConfig.service field instead.
    injectionURL: ""

    # Sidecar injector mutating webhook configuration path value for the clientConfig.service field.
    # Override to pass env variables, for example: /inject/cluster/remote/net/network2
    injectionPath: "/inject"

    injectionCABundle: ""
  telemetry:
    enabled: true
    v2:
      # For Null VM case now.
      # This also enables metadata exchange.
      enabled: true
      # Indicate if prometheus stats filter is enabled or not
      prometheus:
        enabled: true
      # stackdriver filter settings.
      stackdriver:
        enabled: false
  # Revision is set as 'version' label and part of the resource names when installing multiple control planes.
  revision: ""

  # Revision tags are aliases to Istio control plane revisions
  revisionTags: []

  # For Helm compatibility.
  ownerName: ""

  # meshConfig defines runtime configuration of components, including Istiod and istio-agent behavior
  # See https://istio.io/docs/reference/config/istio.mesh.v1alpha1/ for all available options
  meshConfig:
    enablePrometheusMerge: true

  experimental:
    stableValidationPolicy: false

  global:
    # Used to locate istiod.
    istioNamespace: istio-system
    # List of cert-signers to allow "approve" action in the istio cluster role
    #
    # certSigners:
    #   - clusterissuers.cert-manager.io/istio-ca
    certSigners: []
    # enable pod disruption budget for the control plane, which is used to
    # ensure Istio control plane components are gradually upgraded or recovered.
    defaultPodDisruptionBudget:
      enabled: true
      # The values aren't mutable due to a current PodDisruptionBudget limitation
      # minAvailable: 1

    # A minimal set of requested resources to applied to all deployments so that
    # Horizontal Pod Autoscaler will be able to function (if set).
    # Each component can overwrite these default values by adding its own resources
    # block in the relevant section below and setting the desired resources values.
    defaultResources:
      requests:
        cpu: 10m
      #   memory: 128Mi
      # limits:
      #   cpu: 100m
      #   memory: 128Mi

    # Default hub for Istio images.
    # Releases are published to docker hub under 'istio' project.
    # Dev builds from prow are on gcr.io
    hub: docker.io/istio
    # Default tag for Istio images.
    tag: 1.26.2
    # Variant of the image to use.
    # Currently supported are: [debug, distroless]
    variant: ""

    # Specify image pull policy if default behavior isn't desired.
    # Default behavior: latest images will be Always else IfNotPresent.
    imagePullPolicy: ""

    # ImagePullSecrets for all ServiceAccount, list of secrets in the same namespace
    # to use for pulling any images in pods that reference this ServiceAccount.
    # For components that don't use ServiceAccounts (i.e. grafana, servicegraph, tracing)
    # ImagePullSecrets will be added to the corresponding Deployment(StatefulSet) objects.
    # Must be set for any cluster configured with private docker registry.
    imagePullSecrets: []
    # - private-registry-key

    # Enabled by default in master for maximising testing.
    istiod:
      enableAnalysis: false

    # To output all istio components logs in json format by adding --log_as_json argument to each container argument
    logAsJson: false

    # Comma-separated minimum per-scope logging level of messages to output, in the form of <scope>:<level>,<scope>:<level>
    # The control plane has different scopes depending on component, but can configure default log level across all components
    # If empty, default scope and level will be used as configured in code
    logging:
      level: "default:info"

    omitSidecarInjectorConfigMap: false

    # Configure whether Operator manages webhook configurations. The current behavior
    # of Istiod is to manage its own webhook configurations.
    # When this option is set as true, Istio Operator, instead of webhooks, manages the
    # webhook configurations. When this option is set as false, webhooks manage their
    # own webhook configurations.
    operatorManageWebhooks: false

    # Custom DNS config for the pod to resolve names of services in other
    # clusters. Use this to add additional search domains, and other settings.
    # see
    # https://kubernetes.io/docs/concepts/services-networking/dns-pod-service/#dns-config
    # This does not apply to gateway pods as they typically need a different
    # set of DNS settings than the normal application pods (e.g., in
    # multicluster scenarios).
    # NOTE: If using templates, follow the pattern in the commented example below.
    #podDNSSearchNamespaces:
    #- global
    #- "{{ valueOrDefault .DeploymentMeta.Namespace \"default\" }}.global"

    # Kubernetes >=v1.11.0 will create two PriorityClass, including system-cluster-critical and
    # system-node-critical, it is better to configure this in order to make sure your Istio pods
    # will not be killed because of low priority class.
    # Refer to https://kubernetes.io/docs/concepts/configuration/pod-priority-preemption/#priorityclass
    # for more detail.
    priorityClassName: ""

    proxy:
      image: proxyv2

      # This controls the 'policy' in the sidecar injector.
      autoInject: enabled

      # CAUTION: It is important to ensure that all Istio helm charts specify the same clusterDomain value
      # cluster domain. Default value is "cluster.local".
      clusterDomain: "cluster.local"

      # Per Component log level for proxy, applies to gateways and sidecars. If a component level is
      # not set, then the global "logLevel" will be used.
      componentLogLevel: "misc:error"

      # istio ingress capture allowlist
      # examples:
      #     Redirect only selected ports:            --includeInboundPorts="80,8080"
      excludeInboundPorts: ""
      includeInboundPorts: "*"

      # istio egress capture allowlist
      # https://istio.io/docs/tasks/traffic-management/egress.html#calling-external-services-directly
      # example: includeIPRanges: "**********/16,**********/16"
      # would only capture egress traffic on those two IP Ranges, all other outbound traffic would
      # be allowed by the sidecar
      includeIPRanges: "*"
      excludeIPRanges: ""
      includeOutboundPorts: ""
      excludeOutboundPorts: ""

      # Log level for proxy, applies to gateways and sidecars.
      # Expected values are: trace|debug|info|warning|error|critical|off
      logLevel: warning

      # Specify the path to the outlier event log.
      # Example: /dev/stdout
      outlierLogPath: ""

      #If set to true, istio-proxy container will have privileged securityContext
      privileged: false

      # The number of successive failed probes before indicating readiness failure.
      readinessFailureThreshold: 4

      # The initial delay for readiness probes in seconds.
      readinessInitialDelaySeconds: 0

      # The period between readiness probes.
      readinessPeriodSeconds: 15

      # Enables or disables a startup probe.
      # For optimal startup times, changing this should be tied to the readiness probe values.
      #
      # If the probe is enabled, it is recommended to have delay=0s,period=15s,failureThreshold=4.
      # This ensures the pod is marked ready immediately after the startup probe passes (which has a 1s poll interval),
      # and doesn't spam the readiness endpoint too much
      #
      # If the probe is disabled, it is recommended to have delay=1s,period=2s,failureThreshold=30.
      # This ensures the startup is reasonable fast (polling every 2s). 1s delay is used since the startup is not often ready instantly.
      startupProbe:
        enabled: true
        failureThreshold: 600 # 10 minutes

      # Resources for the sidecar.
      resources:
        requests:
          cpu: 100m
          memory: 128Mi
        limits:
          cpu: 2000m
          memory: 1024Mi

      # Default port for Pilot agent health checks. A value of 0 will disable health checking.
      statusPort: 15020

      # Specify which tracer to use. One of: zipkin, lightstep, datadog, stackdriver, none.
      # If using stackdriver tracer outside GCP, set env GOOGLE_APPLICATION_CREDENTIALS to the GCP credential file.
      tracer: "none"

    proxy_init:
      # Base name for the proxy_init container, used to configure iptables.
      image: proxyv2
      # Bypasses iptables idempotency handling, and attempts to apply iptables rules regardless of table state, which may cause unrecoverable failures.
      # Do not use unless you need to work around an issue of the idempotency handling. This flag will be removed in future releases.
      forceApplyIptables: false

    # configure remote pilot and istiod service and endpoint
    remotePilotAddress: ""

    ##############################################################################################
    # The following values are found in other charts. To effectively modify these values, make   #
    # make sure they are consistent across your Istio helm charts                                #
    ##############################################################################################

    # The customized CA address to retrieve certificates for the pods in the cluster.
    # CSR clients such as the Istio Agent and ingress gateways can use this to specify the CA endpoint.
    # If not set explicitly, default to the Istio discovery address.
    caAddress: ""

    # Enable control of remote clusters.
    externalIstiod: false

    # Configure a remote cluster as the config cluster for an external istiod.
    configCluster: false

    # configValidation enables the validation webhook for Istio configuration.
    configValidation: true

    # Mesh ID means Mesh Identifier. It should be unique within the scope where
    # meshes will interact with each other, but it is not required to be
    # globally/universally unique. For example, if any of the following are true,
    # then two meshes must have different Mesh IDs:
    # - Meshes will have their telemetry aggregated in one place
    # - Meshes will be federated together
    # - Policy will be written referencing one mesh from the other
    #
    # If an administrator expects that any of these conditions may become true in
    # the future, they should ensure their meshes have different Mesh IDs
    # assigned.
    #
    # Within a multicluster mesh, each cluster must be (manually or auto)
    # configured to have the same Mesh ID value. If an existing cluster 'joins' a
    # multicluster mesh, it will need to be migrated to the new mesh ID. Details
    # of migration TBD, and it may be a disruptive operation to change the Mesh
    # ID post-install.
    #
    # If the mesh admin does not specify a value, Istio will use the value of the
    # mesh's Trust Domain. The best practice is to select a proper Trust Domain
    # value.
    meshID: ""

    # Configure the mesh networks to be used by the Split Horizon EDS.
    #
    # The following example defines two networks with different endpoints association methods.
    # For `network1` all endpoints that their IP belongs to the provided CIDR range will be
    # mapped to network1. The gateway for this network example is specified by its public IP
    # address and port.
    # The second network, `network2`, in this example is defined differently with all endpoints
    # retrieved through the specified Multi-Cluster registry being mapped to network2. The
    # gateway is also defined differently with the name of the gateway service on the remote
    # cluster. The public IP for the gateway will be determined from that remote service (only
    # LoadBalancer gateway service type is currently supported, for a NodePort type gateway service,
    # it still need to be configured manually).
    #
    # meshNetworks:
    #   network1:
    #     endpoints:
    #     - fromCidr: "***********/24"
    #     gateways:
    #     - address: *******
    #       port: 80
    #   network2:
    #     endpoints:
    #     - fromRegistry: reg1
    #     gateways:
    #     - registryServiceName: istio-ingressgateway.istio-system.svc.cluster.local
    #       port: 443
    #
    meshNetworks: {}

    # Use the user-specified, secret volume mounted key and certs for Pilot and workloads.
    mountMtlsCerts: false

    multiCluster:
      # Set to true to connect two kubernetes clusters via their respective
      # ingressgateway services when pods in each cluster cannot directly
      # talk to one another. All clusters should be using Istio mTLS and must
      # have a shared root CA for this model to work.
      enabled: false
      # Should be set to the name of the cluster this installation will run in. This is required for sidecar injection
      # to properly label proxies
      clusterName: ""

    # Network defines the network this cluster belong to. This name
    # corresponds to the networks in the map of mesh networks.
    network: ""

    # Configure the certificate provider for control plane communication.
    # Currently, two providers are supported: "kubernetes" and "istiod".
    # As some platforms may not have kubernetes signing APIs,
    # Istiod is the default
    pilotCertProvider: istiod

    sds:
      # The JWT token for SDS and the aud field of such JWT. See RFC 7519, section 4.1.3.
      # When a CSR is sent from Istio Agent to the CA (e.g. Istiod), this aud is to make sure the
      # JWT is intended for the CA.
      token:
        aud: istio-ca

    sts:
      # The service port used by Security Token Service (STS) server to handle token exchange requests.
      # Setting this port to a non-zero value enables STS server.
      servicePort: 0

    # The name of the CA for workload certificates.
    # For example, when caName=GkeWorkloadCertificate, GKE workload certificates
    # will be used as the certificates for workloads.
    # The default value is "" and when caName="", the CA will be configured by other
    # mechanisms (e.g., environmental variable CA_PROVIDER).
    caName: ""

    waypoint:
      # Resources for the waypoint proxy.
      resources:
        requests:
          cpu: 100m
          memory: 128Mi
        limits:
          cpu: "2"
          memory: 1Gi

      # If specified, affinity defines the scheduling constraints of waypoint pods.
      affinity: {}

      # Topology Spread Constraints for the waypoint proxy.
      topologySpreadConstraints: []

      # Node labels for the waypoint proxy.
      nodeSelector: {}

      # Tolerations for the waypoint proxy.
      tolerations: []

  base:
    # For istioctl usage to disable istio config crds in base
    enableIstioConfigCRDs: true

  # Gateway Settings
  gateways:
    # Define the security context for the pod.
    # If unset, this will be automatically set to the minimum privileges required to bind to port 80 and 443.
    # On Kubernetes 1.22+, this only requires the `net.ipv4.ip_unprivileged_port_start` sysctl.
    securityContext: {}

    # Set to `type: RuntimeDefault` to use the default profile for templated gateways, if your container runtime supports it
    seccompProfile: {}

  # gatewayClasses allows customizing the configuration of the default deployment of Gateways per GatewayClass.
  # For example:
  # gatewayClasses:
  #   istio:
  #     service:
  #       spec:
  #         type: ClusterIP
  # Per-Gateway configuration can also be set in the `Gateway.spec.infrastructure.parametersRef` field.
  gatewayClasses: {}
