{{- if .Values.resourceQuotas.enabled }}
apiVersion: v1
kind: ResourceQuota
metadata:
  name: {{ include "ztunnel.release-name" . }}
  namespace: {{ .Release.Namespace }}
  labels:
    app.kubernetes.io/name: ztunnel
    {{- include "istio.labels" . | nindent 4}}
    {{ with .Values.labels -}}{{ toYaml . | nindent 4}}{{ end }}
spec:
  hard:
    pods: {{ .Values.resourceQuotas.pods | quote }}
  scopeSelector:
    matchExpressions:
    - operator: In
      scopeName: PriorityClass
      values:
      - system-node-critical
{{- end }}
