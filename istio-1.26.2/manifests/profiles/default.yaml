apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
metadata:
  namespace: istio-system
spec:
  hub: docker.io/istio
  tag: 1.26.2

  # Turn on default components: base, pilot, and ingress gateway
  components:
    base:
      enabled: true
    pilot:
      enabled: true
    # Istio Gateway feature
    ingressGateways:
    - name: istio-ingressgateway
      enabled: true
    egressGateways:
    - name: istio-egressgateway
      enabled: false

  # Most default values come from the helm chart's values.yaml
  # Below are the things that differ
  values:
    defaultRevision: ""
    global:
      istioNamespace: istio-system
      configValidation: true
    gateways:
      istio-ingressgateway: {}
      istio-egressgateway: {}
    ztunnel:
      resourceName: ztunnel
