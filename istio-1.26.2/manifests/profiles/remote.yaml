# The remote profile is used to configure a mesh cluster without a locally deployed control plane.
# Only the injector mutating webhook configuration is installed.
apiVersion: install.istio.io/v1alpha1
kind: IstioOperator
spec:
  components:
    base:
      enabled: true
    pilot:
      enabled: true
    ingressGateways:
    - name: istio-ingressgateway
      enabled: false
  values:
    profile: remote
