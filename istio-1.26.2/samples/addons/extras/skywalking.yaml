apiVersion: apps/v1
kind: Deployment
metadata:
  name: skywalking-oap
  namespace: istio-system
  labels:
    app: skywalking-oap
spec:
  selector:
    matchLabels:
      app: skywalking-oap
  template:
    metadata:
      labels:
        app: skywalking-oap
        sidecar.istio.io/inject: "false"
    spec:
      containers:
        - name: skywalking-oap
          image: apache/skywalking-oap-server:9.7.0
          env:
            - name: SW_HEALTH_CHECKER
              value: default
          readinessProbe:
            exec:
              command:
              - /skywalking/bin/swctl
              - health
            initialDelaySeconds: 30
            periodSeconds: 5

---
apiVersion: v1
kind: Service
metadata:
  name: tracing
  namespace: istio-system
  labels:
    app: skywalking-oap
spec:
  type: ClusterIP
  ports:
    - name: grpc
      port: 11800
      protocol: TCP
      targetPort: 11800
    - name: http-query
      port: 12800
      protocol: TCP
      targetPort: 12800
  selector:
    app: skywalking-oap
---
apiVersion: v1
kind: Service
metadata:
  labels:
    name: skywalking-oap
  name: skywalking-oap
  namespace: istio-system
spec:
  ports:
    - port: 11800
      targetPort: 11800
      name: grpc
    - port: 12800
      targetPort: 12800
      name: http-query
  selector:
    app: skywalking-oap
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: skywalking-ui
  namespace: istio-system
  labels:
    app: skywalking-ui
spec:
  selector:
    matchLabels:
      app: skywalking-ui
  template:
    metadata:
      labels:
        app: skywalking-ui
      annotations:
        sidecar.istio.io/inject: "false"
    spec:
      containers:
        - name: skywalking-ui
          image: apache/skywalking-ui:9.1.0
          env:
            - name: SW_OAP_ADDRESS
              value: http://skywalking-oap:12800
          readinessProbe:
            httpGet:
              path: /
              port: 8080
            initialDelaySeconds: 30
            periodSeconds: 5
---
apiVersion: v1
kind: Service
metadata:
  name: tracing-ui
  namespace: istio-system
  labels:
    app: skywalking-ui
spec:
  type: ClusterIP
  ports:
    - name: http
      port: 8080
      protocol: TCP
      targetPort: 8080
  selector:
    app: skywalking-ui
---
apiVersion: v1
kind: Service
metadata:
  labels:
    name: skywalking-ui
  name: skywalking-ui
  namespace: istio-system
spec:
  ports:
    - port: 8080
      targetPort: 8080
      name: http
  selector:
    app: skywalking-ui
