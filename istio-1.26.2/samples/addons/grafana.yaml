---
# Source: grafana/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
automountServiceAccountToken: false
metadata:
  labels:
    helm.sh/chart: grafana-8.6.3
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: grafana
    app.kubernetes.io/version: "11.3.1"
  name: grafana
  namespace: istio-system
---
# Source: grafana/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: grafana
  namespace: istio-system
  labels:
    helm.sh/chart: grafana-8.6.3
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: grafana
    app.kubernetes.io/version: "11.3.1"
data:
  
  grafana.ini: |
    [analytics]
    check_for_updates = true
    [grafana_net]
    url = https://grafana.net
    [log]
    mode = console
    [paths]
    data = /var/lib/grafana/
    logs = /var/log/grafana
    plugins = /var/lib/grafana/plugins
    provisioning = /etc/grafana/provisioning
    [server]
    domain = ''
  datasources.yaml: |
    apiVersion: 1
    datasources:
    - access: proxy
      editable: true
      isDefault: true
      jsonData:
        timeInterval: 15s
      name: Prometheus
      orgId: 1
      type: prometheus
      url: http://prometheus:9090
    - access: proxy
      editable: true
      isDefault: false
      jsonData:
        timeInterval: 5s
      name: Loki
      orgId: 1
      type: loki
      url: http://loki:3100
  dashboardproviders.yaml: |
    apiVersion: 1
    providers:
    - disableDeletion: false
      folder: istio
      name: istio
      options:
        path: /var/lib/grafana/dashboards/istio
      orgId: 1
      type: file
    - disableDeletion: false
      folder: istio
      name: istio-services
      options:
        path: /var/lib/grafana/dashboards/istio-services
      orgId: 1
      type: file
---
# Source: grafana/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: grafana
  namespace: istio-system
  labels:
    helm.sh/chart: grafana-8.6.3
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: grafana
    app.kubernetes.io/version: "11.3.1"
spec:
  type: ClusterIP
  ports:
    - name: service
      port: 3000
      protocol: TCP
      targetPort: 3000
  selector:
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: grafana
---
# Source: grafana/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: grafana
  namespace: istio-system
  labels:
    helm.sh/chart: grafana-8.6.3
    app.kubernetes.io/name: grafana
    app.kubernetes.io/instance: grafana
    app.kubernetes.io/version: "11.3.1"
spec:
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app.kubernetes.io/name: grafana
      app.kubernetes.io/instance: grafana
  strategy:
    type: RollingUpdate
  template:
    metadata:
      labels:
        helm.sh/chart: grafana-8.6.3
        app.kubernetes.io/name: grafana
        app.kubernetes.io/instance: grafana
        app.kubernetes.io/version: "11.3.1"
        sidecar.istio.io/inject: "false"
      annotations:
        checksum/config: 68c00ea91b7ba5c04efc55555ef2307f52aaf738b04e9665e91bb756c8f32b22
        checksum/sc-dashboard-provider-config: e70bf6a851099d385178a76de9757bb0bef8299da6d8443602590e44f05fdf24
        kubectl.kubernetes.io/default-container: grafana
    spec:
      
      serviceAccountName: grafana
      automountServiceAccountToken: true
      enableServiceLinks: true
      containers:
        - name: grafana
          image: "docker.io/grafana/grafana:11.3.1"
          imagePullPolicy: IfNotPresent
          securityContext:
            allowPrivilegeEscalation: false
            capabilities:
              drop:
              - ALL
            seccompProfile:
              type: RuntimeDefault
          volumeMounts:
            - name: config
              mountPath: "/etc/grafana/grafana.ini"
              subPath: grafana.ini
            - name: storage
              mountPath: "/var/lib/grafana"
            - name: dashboards-istio
              mountPath: "/var/lib/grafana/dashboards/istio"
            - name: dashboards-istio-services
              mountPath: "/var/lib/grafana/dashboards/istio-services"
            - name: config
              mountPath: "/etc/grafana/provisioning/datasources/datasources.yaml"
              subPath: "datasources.yaml"
            - name: config
              mountPath: "/etc/grafana/provisioning/dashboards/dashboardproviders.yaml"
              subPath: "dashboardproviders.yaml"
          ports:
            - name: grafana
              containerPort: 3000
              protocol: TCP
            - name: gossip-tcp
              containerPort: 9094
              protocol: TCP
            - name: gossip-udp
              containerPort: 9094
              protocol: UDP
          env:
            - name: POD_IP
              valueFrom:
                fieldRef:
                  fieldPath: status.podIP
            - name: GF_PATHS_DATA
              value: /var/lib/grafana/
            - name: GF_PATHS_LOGS
              value: /var/log/grafana
            - name: GF_PATHS_PLUGINS
              value: /var/lib/grafana/plugins
            - name: GF_PATHS_PROVISIONING
              value: /etc/grafana/provisioning
            - name: "GF_AUTH_ANONYMOUS_ENABLED"
              value: "true"
            - name: "GF_AUTH_ANONYMOUS_ORG_ROLE"
              value: "Admin"
            - name: "GF_AUTH_BASIC_ENABLED"
              value: "false"
            - name: "GF_SECURITY_ADMIN_PASSWORD"
              value: "admin"
            - name: "GF_SECURITY_ADMIN_USER"
              value: "admin"
          livenessProbe:
            failureThreshold: 10
            httpGet:
              path: /api/health
              port: 3000
            initialDelaySeconds: 60
            timeoutSeconds: 30
          readinessProbe:
            httpGet:
              path: /api/health
              port: 3000
      volumes:
        - name: config
          configMap:
            name: grafana
        - name: dashboards-istio
          configMap:
            name: istio-grafana-dashboards
        - name: dashboards-istio-services
          configMap:
            name: istio-services-grafana-dashboards
        - name: storage
          emptyDir: {}

---

apiVersion: v1
data:
  istio-performance-dashboard.json: |
    {"annotations":{"list":[{"builtIn":1,"datasource":{"type":"datasource","uid":"grafana"},"enable":true,"hide":true,"iconColor":"rgba(0, 211, 255, 1)","name":"Annotations & Alerts","type":"dashboard"}]},"editable":true,"fiscalYearStartMonth":0,"graphTooltip":0,"links":[],"liveNow":false,"panels":[{"collapsed":false,"datasource":{"type":"prometheus","uid":"${datasource}"},"gridPos":{"h":1,"w":24,"x":0,"y":0},"id":21,"panels":[],"targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"refId":"A"}],"title":"Performance Dashboard Notes","type":"row"},{"gridPos":{"h":6,"w":24,"x":0,"y":1},"id":19,"links":[],"options":{"code":{"language":"plaintext","showLineNumbers":false,"showMiniMap":false},"content":"The charts on this dashboard are intended to show Istio main components cost in terms of resources utilization under steady load.\n\n- **vCPU / 1k rps:** shows vCPU utilization by the main Istio components normalized by 1000 requests/second. When idle or low traffic, this chart will be blank. The curve for istio-proxy refers to the services sidecars only.\n- **vCPU:** vCPU utilization by Istio components, not normalized.\n- **Memory:** memory footprint for the components. Telemetry and policy are normalized by 1k rps, and no data is shown  when there is no traffic. For ingress and istio-proxy, the data is per instance.\n- **Bytes transferred / sec:** shows the number of bytes flowing through each Istio component.\n\n\n","mode":"markdown"},"pluginVersion":"10.1.5","title":"Performance Dashboard README","transparent":true,"type":"text"},{"collapsed":false,"datasource":{"type":"prometheus","uid":"${datasource}"},"gridPos":{"h":1,"w":24,"x":0,"y":7},"id":6,"panels":[],"targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"refId":"A"}],"title":"vCPU Usage","type":"row"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":8,"w":12,"x":0,"y":8},"id":4,"links":[],"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"(sum(irate(container_cpu_usage_seconds_total{pod=~\"istio-ingressgateway-.*\",container=\"istio-proxy\"}[$__rate_interval])) / (round(sum(irate(istio_requests_total{source_workload=\"istio-ingressgateway\", reporter=\"source\"}[$__rate_interval])), 0.001)/1000))","format":"time_series","hide":false,"intervalFactor":1,"legendFormat":"istio-ingressgateway","refId":"A"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"(sum(irate(container_cpu_usage_seconds_total{namespace!=\"istio-system\",container=\"istio-proxy\"}[$__rate_interval]))/ (round(sum(irate(istio_requests_total[$__rate_interval])), 0.001)/1000))/ (sum(irate(istio_requests_total{source_workload=\"istio-ingressgateway\"}[$__rate_interval])) >bool 10)","format":"time_series","intervalFactor":1,"legendFormat":"istio-proxy","refId":"B"}],"title":"vCPU / 1k rps","type":"timeseries"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":8,"w":12,"x":12,"y":8},"id":7,"links":[],"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(rate(container_cpu_usage_seconds_total{pod=~\"istio-ingressgateway-.*\",container=\"istio-proxy\"}[$__rate_interval]))","format":"time_series","intervalFactor":1,"legendFormat":"istio-ingressgateway","refId":"A"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(rate(container_cpu_usage_seconds_total{namespace!=\"istio-system\",container=\"istio-proxy\"}[$__rate_interval]))","format":"time_series","intervalFactor":1,"legendFormat":"istio-proxy","refId":"B"}],"title":"vCPU","type":"timeseries"},{"collapsed":false,"datasource":{"type":"prometheus","uid":"${datasource}"},"gridPos":{"h":1,"w":24,"x":0,"y":16},"id":13,"panels":[],"targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"refId":"A"}],"title":"Memory and Data Rates","type":"row"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"bytes"},"overrides":[]},"gridPos":{"h":8,"w":12,"x":0,"y":17},"id":902,"links":[],"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(container_memory_working_set_bytes{pod=~\"istio-ingressgateway-.*\"}) / count(container_memory_working_set_bytes{pod=~\"istio-ingressgateway-.*\",container!=\"POD\"})","format":"time_series","intervalFactor":1,"legendFormat":"per istio-ingressgateway","refId":"A"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(container_memory_working_set_bytes{namespace!=\"istio-system\",container=\"istio-proxy\"}) / count(container_memory_working_set_bytes{namespace!=\"istio-system\",container=\"istio-proxy\"})","format":"time_series","intervalFactor":1,"legendFormat":"per istio proxy","refId":"B"}],"title":"Memory Usage","type":"timeseries"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"Bps"},"overrides":[]},"gridPos":{"h":8,"w":12,"x":12,"y":17},"id":11,"links":[],"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(irate(istio_response_bytes_sum{source_workload=\"istio-ingressgateway\", reporter=\"source\"}[$__rate_interval]))","format":"time_series","intervalFactor":1,"legendFormat":"istio-ingressgateway","refId":"A"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(irate(istio_response_bytes_sum{source_workload_namespace!=\"istio-system\", reporter=\"source\"}[$__rate_interval])) + sum(irate(istio_request_bytes_sum{source_workload_namespace!=\"istio-system\", reporter=\"source\"}[$__rate_interval]))","format":"time_series","intervalFactor":1,"legendFormat":"istio-proxy","refId":"B"}],"title":"Bytes transferred / sec","type":"timeseries"},{"collapsed":false,"datasource":{"type":"prometheus","uid":"${datasource}"},"gridPos":{"h":1,"w":24,"x":0,"y":25},"id":17,"panels":[],"targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"refId":"A"}],"title":"Istio Component Versions","type":"row"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":8,"w":24,"x":0,"y":26},"id":15,"links":[],"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(istio_build) by (component, tag)","format":"time_series","intervalFactor":1,"legendFormat":"{{ component }}: {{ tag }}","refId":"A"}],"title":"Istio Components by Version","type":"timeseries"},{"collapsed":false,"datasource":{"type":"prometheus","uid":"${datasource}"},"gridPos":{"h":1,"w":24,"x":0,"y":34},"id":71,"panels":[],"targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"refId":"A"}],"title":"Proxy Resource Usage","type":"row"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"bytes"},"overrides":[]},"gridPos":{"h":7,"w":6,"x":0,"y":35},"id":72,"links":[],"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(container_memory_working_set_bytes{container=\"istio-proxy\"})","format":"time_series","hide":false,"intervalFactor":2,"legendFormat":"Total (k8s)","refId":"A","step":2}],"title":"Memory","type":"timeseries"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":7,"w":6,"x":6,"y":35},"id":73,"links":[],"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(rate(container_cpu_usage_seconds_total{container=\"istio-proxy\"}[$__rate_interval]))","format":"time_series","hide":false,"intervalFactor":2,"legendFormat":"Total (k8s)","refId":"A","step":2}],"title":"vCPU","type":"timeseries"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"bytes"},"overrides":[]},"gridPos":{"h":7,"w":6,"x":12,"y":35},"id":702,"links":[],"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(container_fs_usage_bytes{container=\"istio-proxy\"})","format":"time_series","intervalFactor":2,"legendFormat":"Total (k8s)","refId":"A","step":2}],"title":"Disk","type":"timeseries"},{"collapsed":false,"datasource":{"type":"prometheus","uid":"${datasource}"},"gridPos":{"h":1,"w":24,"x":0,"y":42},"id":69,"panels":[],"targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"refId":"A"}],"title":"Istiod Resource Usage","type":"row"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"bytes"},"overrides":[]},"gridPos":{"h":7,"w":6,"x":0,"y":43},"id":5,"links":[],"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"process_virtual_memory_bytes{app=\"istiod\"}","format":"time_series","instant":false,"intervalFactor":2,"legendFormat":"Virtual Memory","refId":"I","step":2},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"process_resident_memory_bytes{app=\"istiod\"}","format":"time_series","intervalFactor":2,"legendFormat":"Resident Memory","refId":"H","step":2},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"go_memstats_heap_sys_bytes{app=\"istiod\"}","format":"time_series","hide":true,"intervalFactor":2,"legendFormat":"heap sys","refId":"A"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"go_memstats_heap_alloc_bytes{app=\"istiod\"}","format":"time_series","hide":true,"intervalFactor":2,"legendFormat":"heap alloc","refId":"D"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"go_memstats_alloc_bytes{app=\"istiod\"}","format":"time_series","intervalFactor":2,"legendFormat":"Alloc","refId":"F","step":2},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"go_memstats_heap_inuse_bytes{app=\"istiod\"}","format":"time_series","hide":false,"intervalFactor":2,"legendFormat":"Heap in-use","refId":"E","step":2},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"go_memstats_stack_inuse_bytes{app=\"istiod\"}","format":"time_series","intervalFactor":2,"legendFormat":"Stack in-use","refId":"G","step":2},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(container_memory_working_set_bytes{container=~\"discovery|istio-proxy\", pod=~\"istiod-.*\"})","format":"time_series","hide":false,"intervalFactor":2,"legendFormat":"Total (k8s)","refId":"C","step":2},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"container_memory_working_set_bytes{container=~\"discovery|istio-proxy\", pod=~\"istiod-.*\"}","format":"time_series","hide":false,"intervalFactor":2,"legendFormat":"{{ container }} (k8s)","refId":"B","step":2}],"title":"Memory","type":"timeseries"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":7,"w":6,"x":6,"y":43},"id":602,"links":[],"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(rate(container_cpu_usage_seconds_total{container=~\"discovery|istio-proxy\", pod=~\"istiod-.*\"}[$__rate_interval]))","format":"time_series","hide":false,"intervalFactor":2,"legendFormat":"Total (k8s)","refId":"A","step":2},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(rate(container_cpu_usage_seconds_total{container=~\"discovery|istio-proxy\", pod=~\"istiod-.*\"}[$__rate_interval])) by (container)","format":"time_series","hide":false,"intervalFactor":2,"legendFormat":"{{ container }} (k8s)","refId":"B","step":2},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"irate(process_cpu_seconds_total{app=\"istiod\"}[$__rate_interval])","format":"time_series","hide":false,"intervalFactor":2,"legendFormat":"pilot (self-reported)","refId":"C","step":2}],"title":"vCPU","type":"timeseries"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"bytes"},"overrides":[]},"gridPos":{"h":7,"w":6,"x":12,"y":43},"id":74,"links":[],"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"process_open_fds{app=\"istiod\"}","format":"time_series","hide":true,"instant":false,"interval":"","intervalFactor":2,"legendFormat":"Open FDs (pilot)","refId":"A"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"container_fs_usage_bytes{ container=~\"discovery|istio-proxy\", pod=~\"istiod-.*\"}","format":"time_series","intervalFactor":2,"legendFormat":"{{ container }}","refId":"B","step":2}],"title":"Disk","type":"timeseries"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":7,"w":6,"x":18,"y":43},"id":402,"links":[],"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":false},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"go_goroutines{app=\"istiod\"}","format":"time_series","intervalFactor":2,"legendFormat":"Number of Goroutines","refId":"A","step":2}],"title":"Goroutines","type":"timeseries"}],"refresh":"","schemaVersion":38,"style":"dark","tags":[],"templating":{"list":[{"hide":0,"includeAll":false,"multi":false,"name":"datasource","options":[],"query":"prometheus","queryValue":"","refresh":1,"regex":"","skipUrlSync":false,"type":"datasource"}]},"time":{"from":"now-30m","to":"now"},"timepicker":{"refresh_intervals":["30s","1m","5m","15m","30m","1h","2h","1d"],"time_options":["5m","15m","1h","6h","12h","24h","2d","7d","30d"]},"timezone":"","title":"Istio Performance Dashboard","version":1,"weekStart":""}
  pilot-dashboard.json: |
    {"graphTooltip":1,"panels":[{"collapsed":false,"gridPos":{"h":1,"w":24,"x":0,"y":0},"id":1,"panels":[],"title":"Deployed Versions","type":"row"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Version number of each running instance","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"}}},"gridPos":{"h":5,"w":24,"x":0,"y":1},"id":2,"interval":"5s","options":{"legend":{"calcs":[],"displayMode":"list"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (tag) (istio_build{component=\"pilot\"})","legendFormat":"Version ({{tag}})"}],"title":"Pilot Versions","type":"timeseries"},{"collapsed":false,"gridPos":{"h":1,"w":24,"x":0,"y":1},"id":3,"panels":[],"title":"Resource Usage","type":"row"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Memory usage of each running instance","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"},"unit":"bytes"}},"gridPos":{"h":10,"w":6,"x":0,"y":2},"id":4,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (container_memory_working_set_bytes{container=\"discovery\",pod=~\"istiod-.*\"})","legendFormat":"Container ({{pod}})"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (go_memstats_stack_inuse_bytes{app=\"istiod\"})","legendFormat":"Stack ({{pod}})"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (go_memstats_heap_inuse_bytes{app=\"istiod\"})","legendFormat":"Heap (In Use) ({{pod}})"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (go_memstats_heap_alloc_bytes{app=\"istiod\"})","legendFormat":"Heap (Allocated) ({{pod}})"}],"title":"Memory Usage","type":"timeseries"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Details about memory allocations","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"},"unit":"Bps"},"overrides":[{"matcher":{"id":"byFrameRefID","options":"B"},"properties":[{"id":"custom.axisPlacement","value":"right"},{"id":"unit","value":"c/s"}]}]},"gridPos":{"h":10,"w":6,"x":6,"y":2},"id":5,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (rate(go_memstats_alloc_bytes_total{app=\"istiod\"}[$__rate_interval]))","legendFormat":"Bytes ({{pod}})"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (rate(go_memstats_mallocs_total{app=\"istiod\"}[$__rate_interval]))","legendFormat":"Objects ({{pod}})"}],"title":"Memory Allocations","type":"timeseries"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"CPU usage of each running instance","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"}}},"gridPos":{"h":10,"w":6,"x":12,"y":2},"id":6,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (irate(container_cpu_usage_seconds_total{container=\"discovery\",pod=~\"istiod-.*\"}[$__rate_interval]))","legendFormat":"Container ({{pod}})"}],"title":"CPU Usage","type":"timeseries"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Goroutine count for each running instance","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"}}},"gridPos":{"h":10,"w":6,"x":18,"y":2},"id":7,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (go_goroutines{app=\"istiod\"})","legendFormat":"Goroutines ({{pod}})"}],"title":"Goroutines","type":"timeseries"},{"collapsed":false,"gridPos":{"h":1,"w":24,"x":0,"y":3},"id":8,"panels":[],"title":"Push Information","type":"row"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"fieldConfig":{"defaults":{"custom":{"drawStyle":"bars","fillOpacity":100,"gradientMode":"none","showPoints":"never","stacking":{"mode":"normal"}},"unit":"ops"},"overrides":[{"matcher":{"id":"byName","options":"cds"},"properties":[{"id":"displayName","value":"Clusters"}]},{"matcher":{"id":"byName","options":"eds"},"properties":[{"id":"displayName","value":"Endpoints"}]},{"matcher":{"id":"byName","options":"lds"},"properties":[{"id":"displayName","value":"Listeners"}]},{"matcher":{"id":"byName","options":"rds"},"properties":[{"id":"displayName","value":"Routes"}]},{"matcher":{"id":"byName","options":"nds"},"properties":[{"id":"displayName","value":"DNS Tables"}]},{"matcher":{"id":"byName","options":"istio.io/debug"},"properties":[{"id":"displayName","value":"Debug"}]},{"matcher":{"id":"byName","options":"istio.io/debug/syncz"},"properties":[{"id":"displayName","value":"Debug"}]},{"matcher":{"id":"byName","options":"wads"},"properties":[{"id":"displayName","value":"Authorization"}]},{"matcher":{"id":"byName","options":"wds"},"properties":[{"id":"displayName","value":"Workloads"}]},{"matcher":{"id":"byName","options":"type.googleapis.com/istio.security.Authorization"},"properties":[{"id":"displayName","value":"Authorizations"}]},{"matcher":{"id":"byName","options":"type.googleapis.com/istio.workload.Address"},"properties":[{"id":"displayName","value":"Addresses"}]}]},"gridPos":{"h":10,"w":8,"x":0,"y":4},"id":9,"interval":"15s","options":{"legend":{"calcs":[],"displayMode":"list"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (type) (irate(pilot_xds_pushes[$__rate_interval]))","legendFormat":"{{type}}"}],"title":"XDS Pushes","type":"timeseries"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Size of each xDS push.\n","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"}}},"gridPos":{"h":10,"w":8,"x":8,"y":4},"id":10,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (type,event) (rate(pilot_k8s_reg_events[$__rate_interval]))","legendFormat":"{{event}} {{type}}"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (type,event) (rate(pilot_k8s_cfg_events[$__rate_interval]))","legendFormat":"{{event}} {{type}}"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (type) (rate(pilot_push_triggers[$__rate_interval]))","legendFormat":"Push {{type}}"}],"title":"Events","type":"timeseries"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Total number of XDS connections\n","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"}}},"gridPos":{"h":10,"w":8,"x":16,"y":4},"id":11,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum(envoy_cluster_upstream_cx_active{cluster_name=\"xds-grpc\"})","legendFormat":"Connections (client reported)"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum (pilot_xds)","legendFormat":"Connections (server reported)"}],"title":"Connections","type":"timeseries"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Number of push errors. Many of these are at least potentional fatal and should be explored in-depth via Istiod logs.\nNote: metrics here do not use rate() to avoid missing transition from \"No series\"; series are not reported if there are no errors at all.\n","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"}}},"gridPos":{"h":10,"w":8,"x":0,"y":14},"id":12,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (type) (pilot_total_xds_rejects)","legendFormat":"Rejected Config ({{type}})"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"pilot_total_xds_internal_errors","legendFormat":"Internal Errors"}],"title":"Push Errors","type":"timeseries"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Count of active and pending proxies managed by each instance.\nPending is expected to converge to zero.\n","gridPos":{"h":10,"w":8,"x":8,"y":14},"id":13,"interval":"1m","options":{"calculation":{"xBuckets":{"mode":"size","value":"1min"}},"cellGap":0,"color":{"mode":"scheme","scheme":"Spectral","steps":128},"yAxis":{"decimals":0,"unit":"s"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum(rate(pilot_xds_push_time_bucket{}[1m])) by (le)","format":"heatmap","legendFormat":"{{le}}"}],"title":"Push Time","type":"heatmap"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Size of each xDS push.\n","gridPos":{"h":10,"w":8,"x":16,"y":14},"id":14,"interval":"1m","options":{"calculation":{"xBuckets":{"mode":"size","value":"1min"}},"cellGap":0,"color":{"mode":"scheme","scheme":"Spectral","steps":128},"yAxis":{"decimals":0,"unit":"bytes"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum(rate(pilot_xds_config_size_bytes_bucket{}[1m])) by (le)","format":"heatmap","legendFormat":"{{le}}"}],"title":"Push Size","type":"heatmap"},{"collapsed":false,"gridPos":{"h":1,"w":24,"x":0,"y":100},"id":15,"panels":[],"title":"Webhooks","type":"row"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Rate of XDS push operations, by type. This is incremented on a per-proxy basis.\n","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"}}},"gridPos":{"h":8,"w":12,"x":0,"y":101},"id":16,"interval":"5s","options":{"legend":{"calcs":[],"displayMode":"list"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum (rate(galley_validation_passed[$__rate_interval]))","legendFormat":"Success"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum (rate(galley_validation_failed[$__rate_interval]))","legendFormat":"Failure"}],"title":"Validation","type":"timeseries"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Size of each xDS push.\n","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"}}},"gridPos":{"h":8,"w":12,"x":12,"y":101},"id":17,"interval":"5s","options":{"legend":{"calcs":[],"displayMode":"list"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum (rate(sidecar_injection_success_total[$__rate_interval]))","legendFormat":"Success"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum (rate(sidecar_injection_failure_total[$__rate_interval]))","legendFormat":"Failure"}],"title":"Injection","type":"timeseries"}],"refresh":"15s","schemaVersion":39,"templating":{"list":[{"name":"datasource","query":"prometheus","type":"datasource"}]},"time":{"from":"now-30m","to":"now"},"timezone":"utc","title":"Istio Control Plane Dashboard","uid":"********************************"}
  ztunnel-dashboard.json: |
    {"graphTooltip":1,"panels":[{"collapsed":false,"gridPos":{"h":1,"w":24,"x":0,"y":0},"id":1,"panels":[],"title":"Process","type":"row"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Version number of each running instance","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"}}},"gridPos":{"h":8,"w":8,"x":0,"y":1},"id":2,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (tag) (istio_build{component=\"ztunnel\"})","legendFormat":"Version ({{tag}})"}],"title":"Ztunnel Versions","type":"timeseries"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Memory usage of each running instance","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"},"unit":"bytes"}},"gridPos":{"h":8,"w":8,"x":8,"y":1},"id":3,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (container_memory_working_set_bytes{container=\"istio-proxy\",pod=~\"ztunnel-.*\"})","legendFormat":"Container ({{pod}})"}],"title":"Memory Usage","type":"timeseries"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"CPU usage of each running instance","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"}}},"gridPos":{"h":8,"w":8,"x":16,"y":1},"id":4,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (irate(container_cpu_usage_seconds_total{container=\"istio-proxy\",pod=~\"ztunnel-.*\"}[$__rate_interval]))","legendFormat":"Container ({{pod}})"}],"title":"CPU Usage","type":"timeseries"},{"collapsed":false,"gridPos":{"h":1,"w":24,"x":0,"y":9},"id":5,"panels":[],"title":"Network","type":"row"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Connections opened and closed per instance","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"},"unit":"cps"}},"gridPos":{"h":8,"w":8,"x":0,"y":10},"id":6,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (rate(istio_tcp_connections_opened_total{pod=~\"ztunnel-.*\"}[$__rate_interval]))","legendFormat":"Opened ({{pod}})"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"-sum by (pod) (rate(istio_tcp_connections_closed_total{pod=~\"ztunnel-.*\"}[$__rate_interval]))","legendFormat":"Closed ({{pod}})"}],"title":"Connections","type":"timeseries"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Bytes sent and received per instance","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"},"unit":"Bps"}},"gridPos":{"h":8,"w":8,"x":8,"y":10},"id":7,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (rate(istio_tcp_sent_bytes_total{pod=~\"ztunnel-.*\"}[$__rate_interval]))","legendFormat":"Sent ({{pod}})"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (rate(istio_tcp_received_bytes_total{pod=~\"ztunnel-.*\"}[$__rate_interval]))","legendFormat":"Received ({{pod}})"}],"title":"Bytes Transmitted","type":"timeseries"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"DNS queries received per instance","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"},"unit":"qps"}},"gridPos":{"h":8,"w":8,"x":16,"y":10},"id":8,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (rate(istio_dns_requests_total{pod=~\"ztunnel-.*\"}[$__rate_interval]))","legendFormat":"Request ({{pod}})"}],"title":"DNS Request","type":"timeseries"},{"collapsed":false,"gridPos":{"h":1,"w":24,"x":0,"y":18},"id":9,"panels":[],"title":"Operations","type":"row"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Count of XDS connection terminations.\nThis will typically spike every 30min for each instance.\n","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"}}},"gridPos":{"h":8,"w":8,"x":0,"y":19},"id":10,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (rate(istio_xds_connection_terminations_total{pod=~\"ztunnel-.*\"}[$__rate_interval]))","legendFormat":"XDS Connection Terminations ({{pod}})"}],"title":"XDS Connections","type":"timeseries"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"fieldConfig":{"defaults":{"custom":{"drawStyle":"bars","fillOpacity":100,"gradientMode":"none","showPoints":"never","stacking":{"mode":"normal"}},"unit":"ops"},"overrides":[{"matcher":{"id":"byName","options":"cds"},"properties":[{"id":"displayName","value":"Clusters"}]},{"matcher":{"id":"byName","options":"eds"},"properties":[{"id":"displayName","value":"Endpoints"}]},{"matcher":{"id":"byName","options":"lds"},"properties":[{"id":"displayName","value":"Listeners"}]},{"matcher":{"id":"byName","options":"rds"},"properties":[{"id":"displayName","value":"Routes"}]},{"matcher":{"id":"byName","options":"nds"},"properties":[{"id":"displayName","value":"DNS Tables"}]},{"matcher":{"id":"byName","options":"istio.io/debug"},"properties":[{"id":"displayName","value":"Debug"}]},{"matcher":{"id":"byName","options":"istio.io/debug/syncz"},"properties":[{"id":"displayName","value":"Debug"}]},{"matcher":{"id":"byName","options":"wads"},"properties":[{"id":"displayName","value":"Authorization"}]},{"matcher":{"id":"byName","options":"wds"},"properties":[{"id":"displayName","value":"Workloads"}]},{"matcher":{"id":"byName","options":"type.googleapis.com/istio.security.Authorization"},"properties":[{"id":"displayName","value":"Authorizations"}]},{"matcher":{"id":"byName","options":"type.googleapis.com/istio.workload.Address"},"properties":[{"id":"displayName","value":"Addresses"}]}]},"gridPos":{"h":8,"w":8,"x":8,"y":19},"id":11,"interval":"15s","options":{"legend":{"calcs":[],"displayMode":"list"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (url) (irate(istio_xds_message_total{pod=~\"ztunnel-.*\"}[$__rate_interval]))","legendFormat":"{{url}}"}],"title":"XDS Pushes","type":"timeseries"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Count of active and pending proxies managed by each instance.\nPending is expected to converge to zero.\n","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"}}},"gridPos":{"h":8,"w":8,"x":16,"y":19},"id":12,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (workload_manager_active_proxy_count{pod=~\"ztunnel-.*\"})","legendFormat":"Active Proxies ({{pod}})"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (pod) (workload_manager_pending_proxy_count{pod=~\"ztunnel-.*\"})","legendFormat":"Pending Proxies ({{pod}})"}],"title":"Workload Manager","type":"timeseries"}],"refresh":"15s","schemaVersion":39,"templating":{"list":[{"name":"datasource","query":"prometheus","type":"datasource"}]},"time":{"from":"now-30m","to":"now"},"timezone":"utc","title":"Istio Ztunnel Dashboard","uid":"12c58766acc81a1c835dd5059eaf2741"}
kind: ConfigMap
metadata:
  creationTimestamp: null
  name: istio-grafana-dashboards
  namespace: istio-system

---

apiVersion: v1
data:
  istio-extension-dashboard.json: |
    {"annotations":{"list":[{"builtIn":1,"datasource":{"type":"datasource","uid":"grafana"},"enable":true,"hide":true,"iconColor":"rgba(0, 211, 255, 1)","name":"Annotations & Alerts","type":"dashboard"}]},"editable":true,"fiscalYearStartMonth":0,"graphTooltip":0,"links":[],"liveNow":false,"panels":[{"collapsed":false,"datasource":{"type":"prometheus","uid":"${datasource}"},"gridPos":{"h":1,"w":24,"x":0,"y":0},"id":3,"panels":[],"targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"refId":"A"}],"title":"Wasm VMs","type":"row"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"description":"","fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"links":[],"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":8,"w":12,"x":0,"y":1},"id":2,"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"avg(envoy_wasm_envoy_wasm_runtime_null_active)","interval":"","legendFormat":"native","refId":"A"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"avg(envoy_wasm_envoy_wasm_runtime_v8_active)","interval":"","legendFormat":"v8","refId":"B"}],"title":"Active","type":"timeseries"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"links":[],"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":8,"w":12,"x":12,"y":1},"id":6,"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"avg(envoy_wasm_envoy_wasm_runtime_null_created)","interval":"","legendFormat":"native","refId":"A"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"avg(envoy_wasm_envoy_wasm_runtime_v8_created)","interval":"","legendFormat":"v8","refId":"B"}],"title":"Created","type":"timeseries"},{"collapsed":false,"datasource":{"type":"prometheus","uid":"${datasource}"},"gridPos":{"h":1,"w":24,"x":0,"y":9},"id":7,"panels":[],"targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"refId":"A"}],"title":"Wasm Module Remote Load","type":"row"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"links":[],"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":8,"w":8,"x":0,"y":10},"id":11,"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"avg(envoy_wasm_remote_load_cache_entries)","interval":"","legendFormat":"entries","refId":"A"}],"title":"Cache Entry","type":"timeseries"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"links":[],"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":8,"w":8,"x":8,"y":10},"id":8,"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"avg(envoy_wasm_remote_load_cache_hits)","interval":"","legendFormat":"hits","refId":"A"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"avg(envoy_wasm_remote_load_cache_misses)","interval":"","legendFormat":"misses","refId":"B"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"avg(envoy_wasm_remote_load_cache_negative_hits)","interval":"","legendFormat":"negative hits","refId":"C"}],"title":"Cache Visit","type":"timeseries"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"links":[],"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":8,"w":8,"x":16,"y":10},"id":10,"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"avg(envoy_wasm_remote_load_fetch_failures)","interval":"","legendFormat":"failures","refId":"A"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"avg(envoy_wasm_remote_load_fetch_successes)","interval":"","legendFormat":"successes","refId":"B"}],"title":"Remote Fetch","type":"timeseries"},{"collapsed":false,"datasource":{"type":"prometheus","uid":"${datasource}"},"gridPos":{"h":1,"w":24,"x":0,"y":18},"id":71,"panels":[],"targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"refId":"A"}],"title":"Proxy Resource Usage","type":"row"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"bytes"},"overrides":[]},"gridPos":{"h":8,"w":12,"x":0,"y":19},"id":72,"links":[],"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(container_memory_working_set_bytes{container=\"istio-proxy\"})","format":"time_series","hide":false,"intervalFactor":2,"legendFormat":"Total (k8s)","refId":"A","step":2}],"title":"Memory","type":"timeseries"},{"datasource":{"type":"prometheus","uid":"${datasource}"},"fieldConfig":{"defaults":{"color":{"mode":"palette-classic"},"custom":{"axisCenteredZero":false,"axisColorMode":"text","axisLabel":"","axisPlacement":"auto","barAlignment":0,"drawStyle":"line","fillOpacity":10,"gradientMode":"none","hideFrom":{"legend":false,"tooltip":false,"viz":false},"insertNulls":false,"lineInterpolation":"linear","lineWidth":1,"pointSize":5,"scaleDistribution":{"type":"linear"},"showPoints":"never","spanNulls":false,"stacking":{"group":"A","mode":"none"},"thresholdsStyle":{"mode":"off"}},"mappings":[],"thresholds":{"mode":"absolute","steps":[{"color":"green","value":null},{"color":"red","value":80}]},"unit":"short"},"overrides":[]},"gridPos":{"h":8,"w":12,"x":12,"y":19},"id":73,"links":[],"options":{"legend":{"calcs":[],"displayMode":"list","placement":"bottom","showLegend":true},"tooltip":{"mode":"multi","sort":"none"}},"pluginVersion":"10.1.5","targets":[{"datasource":{"type":"prometheus","uid":"${datasource}"},"expr":"sum(rate(container_cpu_usage_seconds_total{container=\"istio-proxy\"}[$__rate_interval]))","format":"time_series","hide":false,"intervalFactor":2,"legendFormat":"Total (k8s)","refId":"A","step":2}],"title":"vCPU","type":"timeseries"}],"refresh":"","schemaVersion":38,"style":"dark","tags":[],"templating":{"list":[{"current":{"selected":false,"text":"default","value":"default"},"hide":0,"includeAll":false,"multi":false,"name":"datasource","options":[],"query":"prometheus","queryValue":"","refresh":1,"regex":"","skipUrlSync":false,"type":"datasource"}]},"time":{"from":"now-5m","to":"now"},"timepicker":{"refresh_intervals":["30s","1m","5m","15m","30m","1h","2h","1d"]},"timezone":"","title":"Istio Wasm Extension Dashboard","version":1,"weekStart":""}
  istio-mesh-dashboard.json: |
    {"graphTooltip":1,"panels":[{"collapsed":false,"gridPos":{"h":1,"w":24,"x":0,"y":0},"id":1,"panels":[],"title":"Global Traffic","type":"row"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Total requests in the cluster","fieldConfig":{"defaults":{"color":{"fixedColor":"blue","mode":"fixed"},"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"},"unit":"reqps"}},"gridPos":{"h":5,"w":6,"x":0,"y":1},"id":2,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"round(sum (rate(istio_requests_total{reporter=~\"source|waypoint\"}[$__rate_interval])), 0.01)"}],"title":"Traffic Volume","type":"stat"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Total success rate of requests in the cluster","fieldConfig":{"defaults":{"color":{"fixedColor":"blue","mode":"fixed"},"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"},"unit":"percentunit"}},"gridPos":{"h":5,"w":6,"x":6,"y":1},"id":3,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum (rate(istio_requests_total{reporter=~\"source|waypoint\",response_code!~\"5..\"}[$__rate_interval])) / sum (rate(istio_requests_total{reporter=~\"source|waypoint\"}[$__rate_interval]))"}],"title":"Success Rate","type":"stat"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Total 4xx requests in in the cluster","fieldConfig":{"defaults":{"color":{"fixedColor":"blue","mode":"fixed"},"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"},"unit":"reqps"}},"gridPos":{"h":5,"w":6,"x":12,"y":1},"id":4,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"round(sum (rate(istio_requests_total{reporter=~\"source|waypoint\",response_code=~\"4..\"}[$__rate_interval])), 0.01)or vector(0)"}],"title":"4xxs","type":"stat"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Total 5xx requests in in the cluster","fieldConfig":{"defaults":{"color":{"fixedColor":"blue","mode":"fixed"},"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"},"unit":"reqps"}},"gridPos":{"h":5,"w":6,"x":18,"y":1},"id":5,"interval":"5s","options":{"legend":{"calcs":["last","max"],"displayMode":"table"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"round(sum (rate(istio_requests_total{reporter=~\"source|waypoint\",response_code=~\"5..\"}[$__rate_interval])), 0.01)or vector(0)"}],"title":"5xxs","type":"stat"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Request information for HTTP services","fieldConfig":{"overrides":[{"matcher":{"id":"byName","options":"Value #requests"},"properties":[{"id":"displayName","value":"Requests"},{"id":"decimals","value":2},{"id":"unit","value":"reqps"}]},{"matcher":{"id":"byName","options":"Value #p50"},"properties":[{"id":"displayName","value":"P50 Latency"},{"id":"decimals","value":2},{"id":"unit","value":"ms"}]},{"matcher":{"id":"byName","options":"Value #p90"},"properties":[{"id":"displayName","value":"P90 Latency"},{"id":"decimals","value":2},{"id":"unit","value":"ms"}]},{"matcher":{"id":"byName","options":"Value #p99"},"properties":[{"id":"displayName","value":"P99 Latency"},{"id":"decimals","value":2},{"id":"unit","value":"ms"}]},{"matcher":{"id":"byName","options":"Value #success"},"properties":[{"id":"displayName","value":"Success Rate"},{"id":"decimals","value":2},{"id":"unit","value":"percentunit"},{"id":"custom.cellOptions","value":{"type":"color-background"}},{"id":"thresholds","value":{"mode":"absolute","steps":[{"color":"red","value":null},{"color":"yellow","value":"0.95"},{"color":"green","value":1}]}}]},{"matcher":{"id":"byName","options":"destination_workload_var"},"properties":[{"id":"displayName","value":"Workload"}]},{"matcher":{"id":"byName","options":"destination_service"},"properties":[{"id":"displayName","value":"Service"},{"id":"custom.minWidth","value":400}]},{"matcher":{"id":"byName","options":"destination_workload_namespace"},"properties":[{"id":"custom.hidden","value":true}]},{"matcher":{"id":"byName","options":"destination_workload"},"properties":[{"id":"custom.hidden","value":true}]},{"matcher":{"id":"byName","options":"Time"},"properties":[{"id":"custom.hidden","value":true}]}]},"gridPos":{"h":16,"w":24,"y":10},"id":6,"interval":"5s","pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"label_join(sum by (destination_workload,destination_workload_namespace,destination_service) (rate(istio_requests_total{reporter=~\"source|waypoint\"}[$__rate_interval])), \"destination_workload_var\", \".\", \"destination_workload\", \"destination_workload_namespace\")","format":"table","instant":true,"legendFormat":"{{ destination_workload}}.{{ destination_workload_namespace }}","refId":"requests"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"label_join(histogram_quantile(0.5, sum by (le,destination_workload,destination_workload_namespace) (rate(istio_request_duration_milliseconds_bucket{reporter=~\"source|waypoint\"}[$__rate_interval]))), \"destination_workload_var\", \".\", \"destination_workload\", \"destination_workload_namespace\")","format":"table","instant":true,"legendFormat":"{{ destination_workload}}.{{ destination_workload_namespace }}","refId":"p50"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"label_join(histogram_quantile(0.9, sum by (le,destination_workload,destination_workload_namespace) (rate(istio_request_duration_milliseconds_bucket{reporter=~\"source|waypoint\"}[$__rate_interval]))), \"destination_workload_var\", \".\", \"destination_workload\", \"destination_workload_namespace\")","format":"table","instant":true,"legendFormat":"{{ destination_workload}}.{{ destination_workload_namespace }}","refId":"p90"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"label_join(histogram_quantile(0.99, sum by (le,destination_workload,destination_workload_namespace) (rate(istio_request_duration_milliseconds_bucket{reporter=~\"source|waypoint\"}[$__rate_interval]))), \"destination_workload_var\", \".\", \"destination_workload\", \"destination_workload_namespace\")","format":"table","instant":true,"legendFormat":"{{ destination_workload}}.{{ destination_workload_namespace }}","refId":"p99"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"label_join(sum by (destination_workload,destination_workload_namespace) (rate(istio_requests_total{reporter=~\"source|waypoint\",response_code!~\"5..\"}[$__rate_interval]))/sum by (destination_workload,destination_workload_namespace) (rate(istio_requests_total{reporter=~\"source|waypoint\"}[$__rate_interval])), \"destination_workload_var\", \".\", \"destination_workload\", \"destination_workload_namespace\")","format":"table","instant":true,"legendFormat":"{{ destination_workload}}.{{ destination_workload_namespace }}","refId":"success"}],"title":"HTTP/gRPC Workloads","transformations":[{"id":"merge"}],"type":"table"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Bytes sent and recieived information for TCP services","fieldConfig":{"overrides":[{"matcher":{"id":"byName","options":"Value #recv"},"properties":[{"id":"displayName","value":"Bytes Received"},{"id":"decimals","value":2},{"id":"unit","value":"bps"}]},{"matcher":{"id":"byName","options":"Value #sent"},"properties":[{"id":"displayName","value":"Bytes Sent"},{"id":"decimals","value":2},{"id":"unit","value":"bps"}]},{"matcher":{"id":"byName","options":"destination_workload_var"},"properties":[{"id":"displayName","value":"Workload"}]},{"matcher":{"id":"byName","options":"destination_service"},"properties":[{"id":"displayName","value":"Service"},{"id":"custom.minWidth","value":400}]},{"matcher":{"id":"byName","options":"destination_workload_namespace"},"properties":[{"id":"custom.hidden","value":true}]},{"matcher":{"id":"byName","options":"destination_workload"},"properties":[{"id":"custom.hidden","value":true}]},{"matcher":{"id":"byName","options":"Time"},"properties":[{"id":"custom.hidden","value":true}]}]},"gridPos":{"h":16,"w":24,"y":26},"id":7,"interval":"5s","pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"label_join(sum by (destination_workload,destination_workload_namespace,destination_service) (rate(istio_tcp_received_bytes_total{reporter=~\"source|waypoint\"}[$__rate_interval])), \"destination_workload_var\", \".\", \"destination_workload\", \"destination_workload_namespace\")","format":"table","instant":true,"legendFormat":"{{ destination_workload}}.{{ destination_workload_namespace }}","refId":"recv"},{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"label_join(sum by (destination_workload,destination_workload_namespace,destination_service) (rate(istio_tcp_sent_bytes_total{reporter=~\"source|waypoint\"}[$__rate_interval])), \"destination_workload_var\", \".\", \"destination_workload\", \"destination_workload_namespace\")","format":"table","instant":true,"legendFormat":"{{ destination_workload}}.{{ destination_workload_namespace }}","refId":"sent"}],"title":"TCP Workloads","transformations":[{"id":"merge"}],"type":"table"},{"collapsed":false,"gridPos":{"h":1,"w":24,"x":0,"y":42},"id":8,"panels":[],"title":"Istio Component Versions","type":"row"},{"datasource":{"type":"datasource","uid":"-- Mixed --"},"description":"Version number of each running instance","fieldConfig":{"defaults":{"custom":{"fillOpacity":10,"gradientMode":"hue","showPoints":"never"}}},"gridPos":{"h":8,"w":24,"x":0,"y":43},"id":9,"interval":"5s","options":{"legend":{"calcs":[],"displayMode":"list"}},"pluginVersion":"v11.0.0","targets":[{"datasource":{"type":"prometheus","uid":"$datasource"},"expr":"sum by (component,tag) (istio_build)","legendFormat":"{{component}} ({{tag}})"}],"title":"Istio Component Versions","type":"timeseries"}],"refresh":"15s","schemaVersion":39,"templating":{"list":[{"name":"datasource","query":"prometheus","type":"datasource"}]},"time":{"from":"now-30m","to":"now"},"timezone":"utc","title":"Istio Mesh Dashboard","uid":"1a9a8ea49444aae205c7737573e894f9"}
  istio-service-dashboard.json: "{\"annotations\":{\"list\":[{\"builtIn\":1,\"datasource\":{\"type\":\"datasource\",\"uid\":\"grafana\"},\"enable\":true,\"hide\":true,\"iconColor\":\"rgba(0,
    211, 255, 1)\",\"name\":\"Annotations & Alerts\",\"type\":\"dashboard\"}]},\"editable\":true,\"fiscalYearStartMonth\":0,\"graphTooltip\":0,\"links\":[],\"liveNow\":false,\"panels\":[{\"collapsed\":false,\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"gridPos\":{\"h\":1,\"w\":24,\"x\":0,\"y\":0},\"id\":106,\"panels\":[],\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"refId\":\"A\"}],\"title\":\"General\",\"type\":\"row\"},{\"gridPos\":{\"h\":3,\"w\":24,\"x\":0,\"y\":1},\"id\":89,\"links\":[],\"options\":{\"code\":{\"language\":\"plaintext\",\"showLineNumbers\":false,\"showMiniMap\":false},\"content\":\"<div
    class=\\\"dashboard-header text-center\\\">\\n<span>SERVICE: $service</span>\\n</div>\",\"mode\":\"html\"},\"pluginVersion\":\"10.1.5\",\"transparent\":true,\"type\":\"text\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"fixedColor\":\"rgb(31,
    120, 193)\",\"mode\":\"fixed\"},\"mappings\":[{\"options\":{\"match\":\"null\",\"result\":{\"text\":\"N/A\"}},\"type\":\"special\"}],\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"ops\"},\"overrides\":[]},\"gridPos\":{\"h\":4,\"w\":6,\"x\":0,\"y\":4},\"id\":12,\"links\":[],\"maxDataPoints\":100,\"options\":{\"colorMode\":\"none\",\"graphMode\":\"area\",\"justifyMode\":\"auto\",\"orientation\":\"horizontal\",\"reduceOptions\":{\"calcs\":[\"lastNotNull\"],\"fields\":\"\",\"values\":false},\"textMode\":\"auto\"},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_requests_total{reporter=~\\\"$qrep\\\",destination_service=~\\\"$service\\\"}[5m])),
    0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"refId\":\"A\",\"step\":4}],\"title\":\"Client
    Request Volume\",\"type\":\"stat\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"fixedColor\":\"rgb(31,
    120, 193)\",\"mode\":\"fixed\"},\"mappings\":[],\"noValue\":\"0\",\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"dark-red\",\"value\":null},{\"color\":\"dark-yellow\",\"value\":0.95},{\"color\":\"dark-green\",\"value\":0.99}]},\"unit\":\"percentunit\"},\"overrides\":[]},\"gridPos\":{\"h\":4,\"w\":6,\"x\":6,\"y\":4},\"id\":14,\"links\":[],\"maxDataPoints\":100,\"options\":{\"colorMode\":\"none\",\"graphMode\":\"area\",\"justifyMode\":\"auto\",\"orientation\":\"horizontal\",\"reduceOptions\":{\"calcs\":[\"lastNotNull\"],\"fields\":\"\",\"values\":false},\"textMode\":\"auto\"},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"editorMode\":\"code\",\"expr\":\"sum(irate(istio_requests_total{reporter=~\\\"$qrep\\\",destination_service=~\\\"$service\\\",response_code!~\\\"5.*\\\"}[5m]))
    / (sum(irate(istio_requests_total{reporter=~\\\"$qrep\\\",destination_service=~\\\"$service\\\"}[5m]))
    or on () vector(1))\",\"format\":\"time_series\",\"intervalFactor\":1,\"range\":true,\"refId\":\"A\"}],\"title\":\"Client
    Success Rate (non-5xx responses)\",\"type\":\"stat\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"s\"},\"overrides\":[]},\"gridPos\":{\"h\":4,\"w\":6,\"x\":12,\"y\":4},\"id\":87,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"right\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.50,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",destination_service=~\\\"$service\\\"}[1m]))
    by (le)) / 1000) or histogram_quantile(0.50, sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\",destination_service=~\\\"$service\\\"}[1m]))
    by (le))\",\"format\":\"time_series\",\"interval\":\"\",\"intervalFactor\":1,\"legendFormat\":\"P50\",\"refId\":\"A\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.90,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",destination_service=~\\\"$service\\\"}[1m]))
    by (le)) / 1000) or histogram_quantile(0.90, sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\",destination_service=~\\\"$service\\\"}[1m]))
    by (le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"P90\",\"refId\":\"B\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.99,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",destination_service=~\\\"$service\\\"}[1m]))
    by (le)) / 1000) or histogram_quantile(0.99, sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\",destination_service=~\\\"$service\\\"}[1m]))
    by (le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"P99\",\"refId\":\"C\"}],\"title\":\"Client
    Request Duration\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"fixedColor\":\"rgb(31,
    120, 193)\",\"mode\":\"fixed\"},\"mappings\":[{\"options\":{\"match\":\"null\",\"result\":{\"text\":\"N/A\"}},\"type\":\"special\"}],\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"Bps\"},\"overrides\":[]},\"gridPos\":{\"h\":4,\"w\":6,\"x\":18,\"y\":4},\"id\":84,\"links\":[],\"maxDataPoints\":100,\"options\":{\"colorMode\":\"none\",\"graphMode\":\"area\",\"justifyMode\":\"auto\",\"orientation\":\"horizontal\",\"reduceOptions\":{\"calcs\":[\"mean\"],\"fields\":\"\",\"values\":false},\"textMode\":\"auto\"},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"sum(irate(istio_tcp_received_bytes_total{reporter=~\\\"$qrep\\\",
    destination_service=~\\\"$service\\\"}[1m]))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"\",\"refId\":\"A\"}],\"title\":\"TCP
    Received Bytes\",\"type\":\"stat\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"fixedColor\":\"rgb(31,
    120, 193)\",\"mode\":\"fixed\"},\"mappings\":[{\"options\":{\"match\":\"null\",\"result\":{\"text\":\"N/A\"}},\"type\":\"special\"}],\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"ops\"},\"overrides\":[]},\"gridPos\":{\"h\":4,\"w\":6,\"x\":0,\"y\":8},\"id\":97,\"links\":[],\"maxDataPoints\":100,\"options\":{\"colorMode\":\"none\",\"graphMode\":\"area\",\"justifyMode\":\"auto\",\"orientation\":\"horizontal\",\"reduceOptions\":{\"calcs\":[\"lastNotNull\"],\"fields\":\"\",\"values\":false},\"textMode\":\"auto\"},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_requests_total{reporter=\\\"destination\\\",destination_service=~\\\"$service\\\"}[5m])),
    0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"refId\":\"A\",\"step\":4}],\"title\":\"Server
    Request Volume\",\"type\":\"stat\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"fixedColor\":\"rgb(31,
    120, 193)\",\"mode\":\"fixed\"},\"mappings\":[],\"noValue\":\"0\",\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"rgba(50,
    172, 45, 0.97)\",\"value\":null},{\"color\":\"rgba(237, 129, 40, 0.89)\",\"value\":95},{\"color\":\"rgba(245,
    54, 54, 0.9)\",\"value\":99}]},\"unit\":\"percentunit\"},\"overrides\":[]},\"gridPos\":{\"h\":4,\"w\":6,\"x\":6,\"y\":8},\"id\":98,\"links\":[],\"maxDataPoints\":100,\"options\":{\"colorMode\":\"none\",\"graphMode\":\"area\",\"justifyMode\":\"auto\",\"orientation\":\"horizontal\",\"reduceOptions\":{\"calcs\":[\"lastNotNull\"],\"fields\":\"\",\"values\":false},\"textMode\":\"auto\"},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"editorMode\":\"code\",\"expr\":\"sum(irate(istio_requests_total{reporter=\\\"destination\\\",destination_service=~\\\"$service\\\",response_code!~\\\"5.*\\\"}[5m]))
    / (sum(irate(istio_requests_total{reporter=\\\"destination\\\",destination_service=~\\\"$service\\\"}[5m]))
    or on () vector(1))\",\"format\":\"time_series\",\"intervalFactor\":1,\"range\":true,\"refId\":\"A\"}],\"title\":\"Server
    Success Rate (non-5xx responses)\",\"type\":\"stat\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"s\"},\"overrides\":[]},\"gridPos\":{\"h\":4,\"w\":6,\"x\":12,\"y\":8},\"id\":99,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"right\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.50,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"destination\\\",destination_service=~\\\"$service\\\"}[1m]))
    by (le)) / 1000) or histogram_quantile(0.50, sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"destination\\\",destination_service=~\\\"$service\\\"}[1m]))
    by (le))\",\"format\":\"time_series\",\"interval\":\"\",\"intervalFactor\":1,\"legendFormat\":\"P50\",\"refId\":\"A\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.90,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"destination\\\",destination_service=~\\\"$service\\\"}[1m]))
    by (le)) / 1000) or histogram_quantile(0.90, sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"destination\\\",destination_service=~\\\"$service\\\"}[1m]))
    by (le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"P90\",\"refId\":\"B\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.99,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"destination\\\",destination_service=~\\\"$service\\\"}[1m]))
    by (le)) / 1000) or histogram_quantile(0.99, sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"destination\\\",destination_service=~\\\"$service\\\"}[1m]))
    by (le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"P99\",\"refId\":\"C\"}],\"title\":\"Server
    Request Duration\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"fixedColor\":\"rgb(31,
    120, 193)\",\"mode\":\"fixed\"},\"mappings\":[{\"options\":{\"match\":\"null\",\"result\":{\"text\":\"N/A\"}},\"type\":\"special\"}],\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"Bps\"},\"overrides\":[]},\"gridPos\":{\"h\":4,\"w\":6,\"x\":18,\"y\":8},\"id\":100,\"links\":[],\"maxDataPoints\":100,\"options\":{\"colorMode\":\"none\",\"graphMode\":\"area\",\"justifyMode\":\"auto\",\"orientation\":\"horizontal\",\"reduceOptions\":{\"calcs\":[\"mean\"],\"fields\":\"\",\"values\":false},\"textMode\":\"auto\"},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"sum(irate(istio_tcp_sent_bytes_total{reporter=~\\\"$qrep\\\",
    destination_service=~\\\"$service\\\"}[1m]))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"\",\"refId\":\"A\"}],\"title\":\"TCP
    Sent Bytes\",\"type\":\"stat\"},{\"collapsed\":false,\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"gridPos\":{\"h\":1,\"w\":24,\"x\":0,\"y\":12},\"id\":104,\"panels\":[],\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"refId\":\"A\"}],\"title\":\"Client
    Workloads\",\"type\":\"row\"},{\"gridPos\":{\"h\":3,\"w\":24,\"x\":0,\"y\":13},\"id\":45,\"links\":[],\"options\":{\"code\":{\"language\":\"plaintext\",\"showLineNumbers\":false,\"showMiniMap\":false},\"content\":\"<div
    class=\\\"dashboard-header text-center\\\">\\n<span>CLIENT WORKLOADS</span>\\n</div>\",\"mode\":\"html\"},\"pluginVersion\":\"10.1.5\",\"transparent\":true,\"type\":\"text\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":0,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"ops\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":0,\"y\":16},\"id\":25,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"single\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_requests_total{connection_security_policy=\\\"mutual_tls\\\",destination_service=~\\\"$service\\\",reporter=~\\\"$qrep\\\",source_workload=~\\\"$srcwl\\\",source_workload_namespace=~\\\"$srcns\\\"}[5m]))
    by (source_workload, source_workload_namespace, response_code), 0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace }} : {{ response_code }} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_requests_total{connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", reporter=~\\\"$qrep\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[5m])) by (source_workload, source_workload_namespace,
    response_code), 0.001)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace }} : {{ response_code }}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Incoming
    Requests By Source And Response Code\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"max\":1.01,\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"percentunit\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":12,\"y\":16},\"id\":26,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"sum(irate(istio_requests_total{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",response_code!~\\\"5.*\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[5m]))
    by (source_workload, source_workload_namespace) / sum(irate(istio_requests_total{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[5m]))
    by (source_workload, source_workload_namespace)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace }} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"sum(irate(istio_requests_total{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",response_code!~\\\"5.*\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[5m]))
    by (source_workload, source_workload_namespace) / sum(irate(istio_requests_total{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[5m]))
    by (source_workload, source_workload_namespace)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace }}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Incoming
    Success Rate (non-5xx responses) By Source\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"description\":\"\",\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"s\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":8,\"x\":0,\"y\":22},\"id\":27,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.50,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le)) / 1000) or histogram_quantile(0.50,
    sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P50 (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.90,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le)) / 1000) or histogram_quantile(0.90,
    sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P90 (\U0001F510mTLS)\",\"refId\":\"B\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.95,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le)) / 1000) or histogram_quantile(0.95,
    sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P95 (\U0001F510mTLS)\",\"refId\":\"C\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.99,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le)) / 1000) or histogram_quantile(0.99,
    sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P99 (\U0001F510mTLS)\",\"refId\":\"D\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.50,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le)) / 1000) or histogram_quantile(0.50,
    sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P50\",\"refId\":\"E\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.90,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le)) / 1000) or histogram_quantile(0.90,
    sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P90\",\"refId\":\"F\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.95,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le)) / 1000) or histogram_quantile(0.95,
    sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P95\",\"refId\":\"G\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.99,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le)) / 1000) or histogram_quantile(0.99,
    sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P99\",\"refId\":\"H\",\"step\":2}],\"title\":\"Incoming Request Duration By Source\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"decbytes\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":8,\"x\":8,\"y\":22},\"id\":28,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P50 (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    \ P90 (\U0001F510mTLS)\",\"refId\":\"B\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P95 (\U0001F510mTLS)\",\"refId\":\"C\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    \ P99 (\U0001F510mTLS)\",\"refId\":\"D\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P50\",\"refId\":\"E\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P90\",\"refId\":\"F\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P95\",\"refId\":\"G\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P99\",\"refId\":\"H\",\"step\":2}],\"title\":\"Incoming Request Size By Source\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"decbytes\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":8,\"x\":16,\"y\":22},\"id\":68,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P50 (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    \ P90 (\U0001F510mTLS)\",\"refId\":\"B\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P95 (\U0001F510mTLS)\",\"refId\":\"C\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    \ P99 (\U0001F510mTLS)\",\"refId\":\"D\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P50\",\"refId\":\"E\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P90\",\"refId\":\"F\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P95\",\"refId\":\"G\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P99\",\"refId\":\"H\",\"step\":2}],\"title\":\"Response Size By Source\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"Bps\"},\"overrides\":[]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":0,\"y\":28},\"id\":80,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_received_bytes_total{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace), 0.001)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace}} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_received_bytes_total{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace), 0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace}}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Bytes
    Received from Incoming TCP Connection\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"Bps\"},\"overrides\":[]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":12,\"y\":28},\"id\":82,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_sent_bytes_total{connection_security_policy=\\\"mutual_tls\\\",
    reporter=~\\\"$qrep\\\", destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace),
    0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace}} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_sent_bytes_total{connection_security_policy!=\\\"mutual_tls\\\",
    reporter=~\\\"$qrep\\\", destination_service=~\\\"$service\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace),
    0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace}}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Bytes
    Sent to Incoming TCP Connection\",\"type\":\"timeseries\"},{\"collapsed\":false,\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"gridPos\":{\"h\":1,\"w\":24,\"x\":0,\"y\":34},\"id\":102,\"panels\":[],\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"refId\":\"A\"}],\"title\":\"Service
    Workloads\",\"type\":\"row\"},{\"gridPos\":{\"h\":3,\"w\":24,\"x\":0,\"y\":35},\"id\":69,\"links\":[],\"options\":{\"code\":{\"language\":\"plaintext\",\"showLineNumbers\":false,\"showMiniMap\":false},\"content\":\"<div
    class=\\\"dashboard-header text-center\\\">\\n<span>SERVICE WORKLOADS</span>\\n</div>\",\"mode\":\"html\"},\"pluginVersion\":\"10.1.5\",\"transparent\":true,\"type\":\"text\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":0,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"ops\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":0,\"y\":38},\"id\":90,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"single\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_requests_total{connection_security_policy=\\\"mutual_tls\\\",destination_service=~\\\"$service\\\",reporter=\\\"destination\\\",destination_workload=~\\\"$dstwl\\\",destination_workload_namespace=~\\\"$dstns\\\"}[5m]))
    by (destination_workload, destination_workload_namespace, response_code), 0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} : {{ response_code
    }} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_requests_total{connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", reporter=\\\"destination\\\", destination_workload=~\\\"$dstwl\\\",
    destination_workload_namespace=~\\\"$dstns\\\"}[5m])) by (destination_workload,
    destination_workload_namespace, response_code), 0.001)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} : {{ response_code
    }}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Incoming Requests By Destination
    Workload And Response Code\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"max\":1.01,\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"percentunit\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":12,\"y\":38},\"id\":91,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"sum(irate(istio_requests_total{reporter=\\\"destination\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",response_code!~\\\"5.*\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[5m]))
    by (destination_workload, destination_workload_namespace) / sum(irate(istio_requests_total{reporter=\\\"destination\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[5m]))
    by (destination_workload, destination_workload_namespace)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"sum(irate(istio_requests_total{reporter=\\\"destination\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",response_code!~\\\"5.*\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[5m]))
    by (destination_workload, destination_workload_namespace) / sum(irate(istio_requests_total{reporter=\\\"destination\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[5m]))
    by (destination_workload, destination_workload_namespace)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Incoming
    Success Rate (non-5xx responses) By Destination Workload\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"description\":\"\",\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"s\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":8,\"x\":0,\"y\":44},\"id\":94,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.50,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le)) / 1000) or histogram_quantile(0.50,
    sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P50 (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.90,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le)) / 1000) or histogram_quantile(0.90,
    sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P90 (\U0001F510mTLS)\",\"refId\":\"B\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.95,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le)) / 1000) or histogram_quantile(0.95,
    sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P95 (\U0001F510mTLS)\",\"refId\":\"C\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.99,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le)) / 1000) or histogram_quantile(0.99,
    sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P99 (\U0001F510mTLS)\",\"refId\":\"D\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.50,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le)) / 1000) or histogram_quantile(0.50,
    sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P50\",\"refId\":\"E\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.90,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le)) / 1000) or histogram_quantile(0.90,
    sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P90\",\"refId\":\"F\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.95,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le)) / 1000) or histogram_quantile(0.95,
    sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P95\",\"refId\":\"G\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.99,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le)) / 1000) or histogram_quantile(0.99,
    sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"destination\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P99\",\"refId\":\"H\",\"step\":2}],\"title\":\"Incoming
    Request Duration By Service Workload\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"decbytes\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":8,\"x\":8,\"y\":44},\"id\":95,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P50 (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }}  P90 (\U0001F510mTLS)\",\"refId\":\"B\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P95 (\U0001F510mTLS)\",\"refId\":\"C\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }}  P99 (\U0001F510mTLS)\",\"refId\":\"D\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P50\",\"refId\":\"E\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P90\",\"refId\":\"F\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P95\",\"refId\":\"G\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P99\",\"refId\":\"H\",\"step\":2}],\"title\":\"Incoming
    Request Size By Service Workload\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"decbytes\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":8,\"x\":16,\"y\":44},\"id\":96,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P50 (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }}  P90 (\U0001F510mTLS)\",\"refId\":\"B\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P95 (\U0001F510mTLS)\",\"refId\":\"C\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }}  P99 (\U0001F510mTLS)\",\"refId\":\"D\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P50\",\"refId\":\"E\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P90\",\"refId\":\"F\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P95\",\"refId\":\"G\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"destination\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[1m]))
    by (destination_workload, destination_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace }} P99\",\"refId\":\"H\",\"step\":2}],\"title\":\"Response
    Size By Service Workload\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"Bps\"},\"overrides\":[]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":0,\"y\":50},\"id\":92,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_received_bytes_total{reporter=\\\"destination\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[$__rate_interval]))
    by (destination_workload, destination_workload_namespace), 0.001)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace}} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_received_bytes_total{reporter=\\\"destination\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_service=~\\\"$service\\\",
    destination_workload=~\\\"$dstwl\\\", destination_workload_namespace=~\\\"$dstns\\\"}[$__rate_interval]))
    by (destination_workload, destination_workload_namespace), 0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{ destination_workload_namespace}}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Bytes
    Received from Incoming TCP Connection\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"Bps\"},\"overrides\":[]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":12,\"y\":50},\"id\":93,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_sent_bytes_total{connection_security_policy=\\\"mutual_tls\\\",
    reporter=\\\"destination\\\", destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\",
    destination_workload_namespace=~\\\"$dstns\\\"}[$__rate_interval])) by (destination_workload,
    destination_workload_namespace), 0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{destination_workload_namespace }} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_sent_bytes_total{connection_security_policy!=\\\"mutual_tls\\\",
    reporter=\\\"destination\\\", destination_service=~\\\"$service\\\", destination_workload=~\\\"$dstwl\\\",
    destination_workload_namespace=~\\\"$dstns\\\"}[$__rate_interval])) by (destination_workload,
    destination_workload_namespace), 0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_workload }}.{{destination_workload_namespace }}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Bytes
    Sent to Incoming TCP Connection\",\"type\":\"timeseries\"}],\"refresh\":\"1m\",\"schemaVersion\":38,\"style\":\"dark\",\"tags\":[],\"templating\":{\"list\":[{\"hide\":0,\"includeAll\":false,\"multi\":false,\"name\":\"datasource\",\"options\":[],\"query\":\"prometheus\",\"queryValue\":\"\",\"refresh\":1,\"regex\":\"\",\"skipUrlSync\":false,\"type\":\"datasource\"},{\"current\":{\"selected\":false,\"text\":\"details.default.svc.cluster.local\",\"value\":\"details.default.svc.cluster.local\"},\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"definition\":\"\",\"hide\":0,\"includeAll\":false,\"label\":\"Service\",\"multi\":false,\"name\":\"service\",\"options\":[],\"query\":\"query_result(sum(istio_requests_total{})
    by (destination_service) or sum(istio_tcp_sent_bytes_total{}) by (destination_service))\",\"refresh\":1,\"regex\":\"/.*destination_service=\\\"([^\\\"]*).*/\",\"skipUrlSync\":false,\"sort\":0,\"tagValuesQuery\":\"\",\"tagsQuery\":\"\",\"type\":\"query\",\"useTags\":false},{\"current\":{\"selected\":false,\"text\":\"destination\",\"value\":\"destination\"},\"datasource\":\"Prometheus\",\"definition\":\"\",\"hide\":0,\"includeAll\":false,\"label\":\"Reporter\",\"multi\":true,\"name\":\"qrep\",\"options\":[{\"selected\":false,\"text\":\"source\",\"value\":\"source\"},{\"selected\":true,\"text\":\"destination\",\"value\":\"destination\"}],\"query\":\"source,destination\",\"refresh\":1,\"regex\":\"\",\"skipUrlSync\":false,\"sort\":1,\"tagValuesQuery\":\"\",\"tagsQuery\":\"\",\"type\":\"custom\",\"useTags\":false},{\"current\":{\"selected\":false,\"text\":\"All\",\"value\":\"$__all\"},\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"definition\":\"\",\"hide\":0,\"includeAll\":true,\"label\":\"Client
    Cluster\",\"multi\":true,\"name\":\"srccluster\",\"options\":[],\"query\":\"query_result(sum(istio_requests_total{reporter=~\\\"$qrep\\\",
    destination_service=\\\"$service\\\"}) by (source_cluster) or sum(istio_tcp_sent_bytes_total{reporter=~\\\"$qrep\\\",
    destination_service=~\\\"$service\\\"}) by (source_cluster))\",\"refresh\":1,\"regex\":\"/.*cluster=\\\"([^\\\"]*).*/\",\"skipUrlSync\":false,\"sort\":2,\"tagValuesQuery\":\"\",\"tagsQuery\":\"\",\"type\":\"query\",\"useTags\":false},{\"current\":{\"selected\":false,\"text\":\"All\",\"value\":\"$__all\"},\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"definition\":\"\",\"hide\":0,\"includeAll\":true,\"label\":\"Client
    Workload Namespace\",\"multi\":true,\"name\":\"srcns\",\"options\":[],\"query\":\"query_result(sum(istio_requests_total{reporter=~\\\"$qrep\\\",
    destination_service=\\\"$service\\\"}) by (source_workload_namespace) or sum(istio_tcp_sent_bytes_total{reporter=~\\\"$qrep\\\",
    destination_service=~\\\"$service\\\"}) by (source_workload_namespace))\",\"refresh\":1,\"regex\":\"/.*namespace=\\\"([^\\\"]*).*/\",\"skipUrlSync\":false,\"sort\":3,\"tagValuesQuery\":\"\",\"tagsQuery\":\"\",\"type\":\"query\",\"useTags\":false},{\"current\":{\"selected\":false,\"text\":\"All\",\"value\":\"$__all\"},\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"definition\":\"\",\"hide\":0,\"includeAll\":true,\"label\":\"Client
    Workload\",\"multi\":true,\"name\":\"srcwl\",\"options\":[],\"query\":\"query_result(sum(istio_requests_total{reporter=~\\\"$qrep\\\",
    destination_service=~\\\"$service\\\", source_workload_namespace=~\\\"$srcns\\\"})
    by (source_workload) or sum(istio_tcp_sent_bytes_total{reporter=~\\\"$qrep\\\",
    destination_service=~\\\"$service\\\", source_workload_namespace=~\\\"$srcns\\\"})
    by (source_workload))\",\"refresh\":1,\"regex\":\"/.*workload=\\\"([^\\\"]*).*/\",\"skipUrlSync\":false,\"sort\":4,\"tagValuesQuery\":\"\",\"tagsQuery\":\"\",\"type\":\"query\",\"useTags\":false},{\"current\":{\"selected\":false,\"text\":\"All\",\"value\":\"$__all\"},\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"definition\":\"\",\"hide\":0,\"includeAll\":true,\"label\":\"Service
    Workload Cluster\",\"multi\":true,\"name\":\"dstcluster\",\"options\":[],\"query\":\"query_result(sum(istio_requests_total{reporter=\\\"destination\\\",
    destination_service=\\\"$service\\\"}) by (destination_cluster) or sum(istio_tcp_sent_bytes_total{reporter=\\\"destination\\\",
    destination_service=~\\\"$service\\\"}) by (destination_cluster))\",\"refresh\":1,\"regex\":\"/.*cluster=\\\"([^\\\"]*).*/\",\"skipUrlSync\":false,\"sort\":2,\"tagValuesQuery\":\"\",\"tagsQuery\":\"\",\"type\":\"query\",\"useTags\":false},{\"current\":{\"selected\":false,\"text\":\"All\",\"value\":\"$__all\"},\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"definition\":\"\",\"hide\":0,\"includeAll\":true,\"label\":\"Service
    Workload Namespace\",\"multi\":true,\"name\":\"dstns\",\"options\":[],\"query\":\"query_result(sum(istio_requests_total{reporter=\\\"destination\\\",
    destination_service=\\\"$service\\\"}) by (destination_workload_namespace) or
    sum(istio_tcp_sent_bytes_total{reporter=\\\"destination\\\", destination_service=~\\\"$service\\\"})
    by (destination_workload_namespace))\",\"refresh\":1,\"regex\":\"/.*namespace=\\\"([^\\\"]*).*/\",\"skipUrlSync\":false,\"sort\":3,\"tagValuesQuery\":\"\",\"tagsQuery\":\"\",\"type\":\"query\",\"useTags\":false},{\"current\":{\"selected\":false,\"text\":\"All\",\"value\":\"$__all\"},\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"definition\":\"\",\"hide\":0,\"includeAll\":true,\"label\":\"Service
    Workload\",\"multi\":true,\"name\":\"dstwl\",\"options\":[],\"query\":\"query_result(
    sum(istio_requests_total{reporter=\\\"destination\\\", destination_service=~\\\"$service\\\",
    destination_cluster=~\\\"$dstcluster\\\", destination_workload_namespace=~\\\"$dstns\\\"})
    by (destination_workload) or sum(istio_tcp_sent_bytes_total{reporter=\\\"destination\\\",
    destination_service=~\\\"$service\\\", destination_cluster=~\\\"$dstcluster\\\",
    destination_workload_namespace=~\\\"$dstns\\\"}) by (destination_workload))\",\"refresh\":1,\"regex\":\"/.*workload=\\\"([^\\\"]*).*/\",\"skipUrlSync\":false,\"sort\":4,\"tagValuesQuery\":\"\",\"tagsQuery\":\"\",\"type\":\"query\",\"useTags\":false}]},\"time\":{\"from\":\"now-15m\",\"to\":\"now\"},\"timepicker\":{\"refresh_intervals\":[\"5m\",\"15m\",\"30m\",\"1h\",\"2h\",\"1d\"],\"time_options\":[\"5m\",\"15m\",\"1h\",\"6h\",\"12h\",\"24h\",\"2d\",\"7d\",\"30d\"]},\"timezone\":\"\",\"title\":\"Istio
    Service Dashboard\",\"version\":1,\"weekStart\":\"\"}\n"
  istio-workload-dashboard.json: "{\"annotations\":{\"list\":[{\"builtIn\":1,\"datasource\":{\"type\":\"datasource\",\"uid\":\"grafana\"},\"enable\":true,\"hide\":true,\"iconColor\":\"rgba(0,
    211, 255, 1)\",\"name\":\"Annotations & Alerts\",\"type\":\"dashboard\"}]},\"editable\":true,\"fiscalYearStartMonth\":0,\"graphTooltip\":0,\"links\":[],\"liveNow\":false,\"panels\":[{\"collapsed\":false,\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"gridPos\":{\"h\":1,\"w\":24,\"x\":0,\"y\":0},\"id\":95,\"panels\":[],\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"refId\":\"A\"}],\"title\":\"General\",\"type\":\"row\"},{\"gridPos\":{\"h\":3,\"w\":24,\"x\":0,\"y\":1},\"id\":89,\"links\":[],\"options\":{\"code\":{\"language\":\"plaintext\",\"showLineNumbers\":false,\"showMiniMap\":false},\"content\":\"<div
    class=\\\"dashboard-header text-center\\\">\\n<span>WORKLOAD: $workload.$namespace</span>\\n</div>\",\"mode\":\"html\"},\"pluginVersion\":\"10.1.5\",\"transparent\":true,\"type\":\"text\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"fixedColor\":\"rgb(31,
    120, 193)\",\"mode\":\"fixed\"},\"mappings\":[{\"options\":{\"match\":\"null\",\"result\":{\"text\":\"N/A\"}},\"type\":\"special\"}],\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"ops\"},\"overrides\":[]},\"gridPos\":{\"h\":4,\"w\":8,\"x\":0,\"y\":4},\"id\":12,\"links\":[],\"maxDataPoints\":100,\"options\":{\"colorMode\":\"none\",\"graphMode\":\"area\",\"justifyMode\":\"auto\",\"orientation\":\"horizontal\",\"reduceOptions\":{\"calcs\":[\"lastNotNull\"],\"fields\":\"\",\"values\":false},\"textMode\":\"auto\"},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_requests_total{reporter=~\\\"$qrep\\\",destination_workload_namespace=~\\\"$namespace\\\",destination_workload=~\\\"$workload\\\"}[5m])),
    0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"refId\":\"A\",\"step\":4}],\"title\":\"Incoming
    Request Volume\",\"type\":\"stat\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"fixedColor\":\"rgb(31,
    120, 193)\",\"mode\":\"fixed\"},\"mappings\":[{\"options\":{\"match\":\"null\",\"result\":{\"text\":\"N/A\"}},\"type\":\"special\"}],\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"rgba(50,
    172, 45, 0.97)\",\"value\":null},{\"color\":\"rgba(237, 129, 40, 0.89)\",\"value\":95},{\"color\":\"rgba(245,
    54, 54, 0.9)\",\"value\":99}]},\"unit\":\"percentunit\"},\"overrides\":[]},\"gridPos\":{\"h\":4,\"w\":8,\"x\":8,\"y\":4},\"id\":14,\"links\":[],\"maxDataPoints\":100,\"options\":{\"colorMode\":\"none\",\"graphMode\":\"area\",\"justifyMode\":\"auto\",\"orientation\":\"horizontal\",\"reduceOptions\":{\"calcs\":[\"mean\"],\"fields\":\"\",\"values\":false},\"textMode\":\"auto\"},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"sum(irate(istio_requests_total{reporter=~\\\"$qrep\\\",destination_workload_namespace=~\\\"$namespace\\\",destination_workload=~\\\"$workload\\\",response_code!~\\\"5.*\\\"}[5m]))
    / sum(irate(istio_requests_total{reporter=~\\\"$qrep\\\",destination_workload_namespace=~\\\"$namespace\\\",destination_workload=~\\\"$workload\\\"}[5m]))\",\"format\":\"time_series\",\"intervalFactor\":1,\"refId\":\"A\"}],\"title\":\"Incoming
    Success Rate (non-5xx responses)\",\"type\":\"stat\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"s\"},\"overrides\":[]},\"gridPos\":{\"h\":4,\"w\":8,\"x\":16,\"y\":4},\"id\":87,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"right\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.50,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\"}[1m])) by (le)) / 1000) or
    histogram_quantile(0.50, sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\",destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\"}[1m])) by (le))\",\"format\":\"time_series\",\"interval\":\"\",\"intervalFactor\":1,\"legendFormat\":\"P50\",\"refId\":\"A\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.90,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\"}[1m])) by (le)) / 1000) or
    histogram_quantile(0.90, sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\",destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\"}[1m])) by (le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"P90\",\"refId\":\"B\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.99,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\"}[1m])) by (le)) / 1000) or
    histogram_quantile(0.99, sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\",destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\"}[1m])) by (le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"P99\",\"refId\":\"C\"}],\"title\":\"Request
    Duration\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"fixedColor\":\"rgb(31,
    120, 193)\",\"mode\":\"fixed\"},\"mappings\":[{\"options\":{\"match\":\"null\",\"result\":{\"text\":\"N/A\"}},\"type\":\"special\"}],\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"Bps\"},\"overrides\":[]},\"gridPos\":{\"h\":4,\"w\":12,\"x\":0,\"y\":8},\"id\":84,\"links\":[],\"maxDataPoints\":100,\"options\":{\"colorMode\":\"none\",\"graphMode\":\"area\",\"justifyMode\":\"auto\",\"orientation\":\"horizontal\",\"reduceOptions\":{\"calcs\":[\"mean\"],\"fields\":\"\",\"values\":false},\"textMode\":\"auto\"},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"sum(irate(istio_tcp_sent_bytes_total{reporter=~\\\"$qrep\\\",
    destination_workload_namespace=~\\\"$namespace\\\", destination_workload=~\\\"$workload\\\"}[$__rate_interval]))
    + sum(irate(istio_tcp_received_bytes_total{reporter=~\\\"$qrep\\\", destination_workload_namespace=~\\\"$namespace\\\",
    destination_workload=~\\\"$workload\\\"}[$__rate_interval]))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"\",\"refId\":\"A\"}],\"title\":\"TCP
    Server Traffic\",\"type\":\"stat\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"fixedColor\":\"rgb(31,
    120, 193)\",\"mode\":\"fixed\"},\"mappings\":[{\"options\":{\"match\":\"null\",\"result\":{\"text\":\"N/A\"}},\"type\":\"special\"}],\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"Bps\"},\"overrides\":[]},\"gridPos\":{\"h\":4,\"w\":12,\"x\":12,\"y\":8},\"id\":85,\"links\":[],\"maxDataPoints\":100,\"options\":{\"colorMode\":\"none\",\"graphMode\":\"area\",\"justifyMode\":\"auto\",\"orientation\":\"horizontal\",\"reduceOptions\":{\"calcs\":[\"mean\"],\"fields\":\"\",\"values\":false},\"textMode\":\"auto\"},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"sum(irate(istio_tcp_sent_bytes_total{reporter=~\\\"$qrep\\\",
    source_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$workload\\\"}[$__rate_interval]))
    + sum(irate(istio_tcp_received_bytes_total{reporter=~\\\"$qrep\\\", source_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$workload\\\"}[$__rate_interval]))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"\",\"refId\":\"A\"}],\"title\":\"TCP
    Client Traffic\",\"type\":\"stat\"},{\"collapsed\":false,\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"gridPos\":{\"h\":1,\"w\":24,\"x\":0,\"y\":12},\"id\":93,\"panels\":[],\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"refId\":\"A\"}],\"title\":\"Inbound
    Workloads\",\"type\":\"row\"},{\"gridPos\":{\"h\":3,\"w\":24,\"x\":0,\"y\":13},\"id\":45,\"links\":[],\"options\":{\"code\":{\"language\":\"plaintext\",\"showLineNumbers\":false,\"showMiniMap\":false},\"content\":\"<div
    class=\\\"dashboard-header text-center\\\">\\n<span>INBOUND WORKLOADS</span>\\n</div>\",\"mode\":\"html\"},\"pluginVersion\":\"10.1.5\",\"transparent\":true,\"type\":\"text\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":0,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"ops\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":0,\"y\":16},\"id\":25,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"single\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_requests_total{connection_security_policy=\\\"mutual_tls\\\",
    destination_workload_namespace=~\\\"$namespace\\\", destination_workload=~\\\"$workload\\\",
    reporter=~\\\"$qrep\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[5m]))
    by (source_workload, source_workload_namespace, response_code), 0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace }} : {{ response_code }} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_requests_total{connection_security_policy!=\\\"mutual_tls\\\",
    destination_workload_namespace=~\\\"$namespace\\\", destination_workload=~\\\"$workload\\\",
    reporter=~\\\"$qrep\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[5m]))
    by (source_workload, source_workload_namespace, response_code), 0.001)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace }} : {{ response_code }}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Incoming
    Requests By Source And Response Code\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"max\":1.01,\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"percentunit\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":12,\"y\":16},\"id\":26,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"sum(irate(istio_requests_total{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_workload_namespace=~\\\"$namespace\\\",
    destination_workload=~\\\"$workload\\\",response_code!~\\\"5.*\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[5m])) by (source_workload, source_workload_namespace)
    / sum(irate(istio_requests_total{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_workload_namespace=~\\\"$namespace\\\", destination_workload=~\\\"$workload\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[5m]))
    by (source_workload, source_workload_namespace)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace }} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"sum(irate(istio_requests_total{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_workload_namespace=~\\\"$namespace\\\",
    destination_workload=~\\\"$workload\\\",response_code!~\\\"5.*\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[5m])) by (source_workload, source_workload_namespace)
    / sum(irate(istio_requests_total{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_workload_namespace=~\\\"$namespace\\\", destination_workload=~\\\"$workload\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[5m]))
    by (source_workload, source_workload_namespace)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace }}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Incoming
    Success Rate (non-5xx responses) By Source\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"description\":\"\",\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"s\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":8,\"x\":0,\"y\":22},\"id\":27,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.50,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le)) / 1000) or histogram_quantile(0.50, sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P50 (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.90,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le)) / 1000) or histogram_quantile(0.90, sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P90 (\U0001F510mTLS)\",\"refId\":\"B\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.95,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le)) / 1000) or histogram_quantile(0.95, sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P95 (\U0001F510mTLS)\",\"refId\":\"C\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.99,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le)) / 1000) or histogram_quantile(0.99, sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P99 (\U0001F510mTLS)\",\"refId\":\"D\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.50,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le)) / 1000) or histogram_quantile(0.50, sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P50\",\"refId\":\"E\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.90,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le)) / 1000) or histogram_quantile(0.90, sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P90\",\"refId\":\"F\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.95,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le)) / 1000) or histogram_quantile(0.95, sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P95\",\"refId\":\"G\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.99,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le)) / 1000) or histogram_quantile(0.99, sum(irate(istio_request_duration_seconds_bucket{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_workload=~\\\"$workload\\\",
    destination_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$srcwl\\\",
    source_workload_namespace=~\\\"$srcns\\\"}[1m])) by (source_workload, source_workload_namespace,
    le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P99\",\"refId\":\"H\",\"step\":2}],\"title\":\"Incoming Request Duration By Source\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"decbytes\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":8,\"x\":8,\"y\":22},\"id\":28,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P50 (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    \ P90 (\U0001F510mTLS)\",\"refId\":\"B\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P95 (\U0001F510mTLS)\",\"refId\":\"C\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    \ P99 (\U0001F510mTLS)\",\"refId\":\"D\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P50\",\"refId\":\"E\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P90\",\"refId\":\"F\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P95\",\"refId\":\"G\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_request_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P99\",\"refId\":\"H\",\"step\":2}],\"title\":\"Incoming Request Size By Source\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"decbytes\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":8,\"x\":16,\"y\":22},\"id\":68,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P50 (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    \ P90 (\U0001F510mTLS)\",\"refId\":\"B\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P95 (\U0001F510mTLS)\",\"refId\":\"C\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    \ P99 (\U0001F510mTLS)\",\"refId\":\"D\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P50\",\"refId\":\"E\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P90\",\"refId\":\"F\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P95\",\"refId\":\"G\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_response_bytes_bucket{reporter=~\\\"$qrep\\\", connection_security_policy!=\\\"mutual_tls\\\",
    destination_workload=~\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[1m]))
    by (source_workload, source_workload_namespace, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{source_workload}}.{{source_workload_namespace}}
    P99\",\"refId\":\"H\",\"step\":2}],\"title\":\"Response Size By Source\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"Bps\"},\"overrides\":[]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":0,\"y\":28},\"id\":80,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_received_bytes_total{reporter=~\\\"$qrep\\\",
    connection_security_policy=\\\"mutual_tls\\\", destination_workload_namespace=~\\\"$namespace\\\",
    destination_workload=~\\\"$workload\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[$__rate_interval]))
    by (source_workload, source_workload_namespace), 0.001)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace}} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_received_bytes_total{reporter=~\\\"$qrep\\\",
    connection_security_policy!=\\\"mutual_tls\\\", destination_workload_namespace=~\\\"$namespace\\\",
    destination_workload=~\\\"$workload\\\", source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[$__rate_interval]))
    by (source_workload, source_workload_namespace), 0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace}}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Bytes
    Received from Incoming TCP Connection\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"Bps\"},\"overrides\":[]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":12,\"y\":28},\"id\":82,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_sent_bytes_total{connection_security_policy=\\\"mutual_tls\\\",
    reporter=~\\\"$qrep\\\", destination_workload_namespace=~\\\"$namespace\\\", destination_workload=~\\\"$workload\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[$__rate_interval]))
    by (source_workload, source_workload_namespace), 0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace}} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_sent_bytes_total{connection_security_policy!=\\\"mutual_tls\\\",
    reporter=~\\\"$qrep\\\", destination_workload_namespace=~\\\"$namespace\\\", destination_workload=~\\\"$workload\\\",
    source_workload=~\\\"$srcwl\\\", source_workload_namespace=~\\\"$srcns\\\"}[$__rate_interval]))
    by (source_workload, source_workload_namespace), 0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    source_workload }}.{{ source_workload_namespace}}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Bytes
    Sent to Incoming TCP Connection\",\"type\":\"timeseries\"},{\"collapsed\":false,\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"gridPos\":{\"h\":1,\"w\":24,\"x\":0,\"y\":34},\"id\":91,\"panels\":[],\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"refId\":\"A\"}],\"title\":\"Outbound
    Services\",\"type\":\"row\"},{\"gridPos\":{\"h\":3,\"w\":24,\"x\":0,\"y\":35},\"id\":69,\"links\":[],\"options\":{\"code\":{\"language\":\"plaintext\",\"showLineNumbers\":false,\"showMiniMap\":false},\"content\":\"<div
    class=\\\"dashboard-header text-center\\\">\\n<span>OUTBOUND SERVICES</span>\\n</div>\",\"mode\":\"html\"},\"pluginVersion\":\"10.1.5\",\"transparent\":true,\"type\":\"text\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":0,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"ops\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":0,\"y\":38},\"id\":70,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"single\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_requests_total{destination_principal=~\\\"spiffe.*\\\",
    source_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$workload\\\",
    reporter=\\\"source\\\", destination_service=~\\\"$dstsvc\\\"}[5m])) by (destination_service,
    response_code), 0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} : {{ response_code }} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_requests_total{destination_principal!~\\\"spiffe.*\\\",
    source_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$workload\\\",
    reporter=\\\"source\\\", destination_service=~\\\"$dstsvc\\\"}[5m])) by (destination_service,
    response_code), 0.001)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} : {{ response_code }}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Outgoing
    Requests By Destination And Response Code\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"max\":1.01,\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"percentunit\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":12,\"y\":38},\"id\":71,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"sum(irate(istio_requests_total{reporter=\\\"source\\\",
    connection_security_policy=\\\"mutual_tls\\\", source_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$workload\\\",response_code!~\\\"5.*\\\", destination_service=~\\\"$dstsvc\\\"}[5m]))
    by (destination_service) / sum(irate(istio_requests_total{reporter=\\\"source\\\",
    connection_security_policy=\\\"mutual_tls\\\", source_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$workload\\\", destination_service=~\\\"$dstsvc\\\"}[5m]))
    by (destination_service)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"sum(irate(istio_requests_total{reporter=\\\"source\\\",
    connection_security_policy!=\\\"mutual_tls\\\", source_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$workload\\\",response_code!~\\\"5.*\\\", destination_service=~\\\"$dstsvc\\\"}[5m]))
    by (destination_service) / sum(irate(istio_requests_total{reporter=\\\"source\\\",
    connection_security_policy!=\\\"mutual_tls\\\", source_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$workload\\\", destination_service=~\\\"$dstsvc\\\"}[5m]))
    by (destination_service)\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Outgoing Success
    Rate (non-5xx responses) By Destination\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"description\":\"\",\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"s\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":8,\"x\":0,\"y\":44},\"id\":72,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.50,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"source\\\",
    connection_security_policy=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le)) / 1000) or histogram_quantile(0.50, sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"source\\\",
    connection_security_policy=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P50 (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.90,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"source\\\",
    connection_security_policy=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le)) / 1000) or histogram_quantile(0.90, sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"source\\\",
    connection_security_policy=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P90 (\U0001F510mTLS)\",\"refId\":\"B\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.95,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"source\\\",
    connection_security_policy=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le)) / 1000) or histogram_quantile(0.95, sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"source\\\",
    connection_security_policy=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P95 (\U0001F510mTLS)\",\"refId\":\"C\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.99,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"source\\\",
    connection_security_policy=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le)) / 1000) or histogram_quantile(0.99, sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"source\\\",
    connection_security_policy=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P99 (\U0001F510mTLS)\",\"refId\":\"D\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.50,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"source\\\",
    connection_security_policy!=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le)) / 1000) or histogram_quantile(0.50, sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"source\\\",
    connection_security_policy!=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P50\",\"refId\":\"E\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.90,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"source\\\",
    connection_security_policy!=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le)) / 1000) or histogram_quantile(0.90, sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"source\\\",
    connection_security_policy!=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P90\",\"refId\":\"F\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.95,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"source\\\",
    connection_security_policy!=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le)) / 1000) or histogram_quantile(0.95, sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"source\\\",
    connection_security_policy!=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P95\",\"refId\":\"G\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"(histogram_quantile(0.99,
    sum(irate(istio_request_duration_milliseconds_bucket{reporter=\\\"source\\\",
    connection_security_policy!=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le)) / 1000) or histogram_quantile(0.99, sum(irate(istio_request_duration_seconds_bucket{reporter=\\\"source\\\",
    connection_security_policy!=\\\"mutual_tls\\\", source_workload=~\\\"$workload\\\",
    source_workload_namespace=~\\\"$namespace\\\", destination_service=~\\\"$dstsvc\\\"}[1m]))
    by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P99\",\"refId\":\"H\",\"step\":2}],\"title\":\"Outgoing
    Request Duration By Destination\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"decbytes\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":8,\"x\":8,\"y\":44},\"id\":73,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"source\\\", connection_security_policy=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P50 (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"source\\\", connection_security_policy=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P90 (\U0001F510mTLS)\",\"refId\":\"B\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"source\\\", connection_security_policy=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P95 (\U0001F510mTLS)\",\"refId\":\"C\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"source\\\", connection_security_policy=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P99 (\U0001F510mTLS)\",\"refId\":\"D\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"source\\\", connection_security_policy!=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P50\",\"refId\":\"E\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"source\\\", connection_security_policy!=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P90\",\"refId\":\"F\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"source\\\", connection_security_policy!=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P95\",\"refId\":\"G\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_request_bytes_bucket{reporter=\\\"source\\\", connection_security_policy!=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P99\",\"refId\":\"H\",\"step\":2}],\"title\":\"Outgoing
    Request Size By Destination\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"decbytes\"},\"overrides\":[{\"matcher\":{\"id\":\"byValue\",\"options\":{\"op\":\"gte\",\"reducer\":\"allIsNull\",\"value\":0}},\"properties\":[{\"id\":\"custom.hideFrom\",\"value\":{\"legend\":true,\"tooltip\":true,\"viz\":false}}]}]},\"gridPos\":{\"h\":6,\"w\":8,\"x\":16,\"y\":44},\"id\":74,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"source\\\", connection_security_policy=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P50 (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"source\\\", connection_security_policy=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P90 (\U0001F510mTLS)\",\"refId\":\"B\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"source\\\", connection_security_policy=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P95 (\U0001F510mTLS)\",\"refId\":\"C\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"source\\\", connection_security_policy=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }}  P99 (\U0001F510mTLS)\",\"refId\":\"D\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.50,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"source\\\", connection_security_policy!=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P50\",\"refId\":\"E\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.90,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"source\\\", connection_security_policy!=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P90\",\"refId\":\"F\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.95,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"source\\\", connection_security_policy!=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P95\",\"refId\":\"G\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"histogram_quantile(0.99,
    sum(irate(istio_response_bytes_bucket{reporter=\\\"source\\\", connection_security_policy!=\\\"mutual_tls\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\",
    destination_service=~\\\"$dstsvc\\\"}[1m])) by (destination_service, le))\",\"format\":\"time_series\",\"hide\":false,\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} P99\",\"refId\":\"H\",\"step\":2}],\"title\":\"Response
    Size By Destination\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"Bps\"},\"overrides\":[]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":0,\"y\":50},\"id\":76,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_received_bytes_total{connection_security_policy=\\\"mutual_tls\\\",
    reporter=\\\"source\\\", source_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$workload\\\",
    destination_service=~\\\"$dstsvc\\\"}[$__rate_interval])) by (destination_service),
    0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_received_bytes_total{connection_security_policy!=\\\"mutual_tls\\\",
    reporter=\\\"source\\\", source_workload_namespace=~\\\"$namespace\\\", source_workload=~\\\"$workload\\\",
    destination_service=~\\\"$dstsvc\\\"}[$__rate_interval])) by (destination_service),
    0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Bytes Sent on
    Outgoing TCP Connection\",\"type\":\"timeseries\"},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"fieldConfig\":{\"defaults\":{\"color\":{\"mode\":\"palette-classic\"},\"custom\":{\"axisCenteredZero\":false,\"axisColorMode\":\"text\",\"axisLabel\":\"\",\"axisPlacement\":\"auto\",\"barAlignment\":0,\"drawStyle\":\"line\",\"fillOpacity\":10,\"gradientMode\":\"none\",\"hideFrom\":{\"legend\":false,\"tooltip\":false,\"viz\":false},\"insertNulls\":false,\"lineInterpolation\":\"linear\",\"lineWidth\":1,\"pointSize\":5,\"scaleDistribution\":{\"type\":\"linear\"},\"showPoints\":\"never\",\"spanNulls\":false,\"stacking\":{\"group\":\"A\",\"mode\":\"none\"},\"thresholdsStyle\":{\"mode\":\"off\"}},\"mappings\":[],\"min\":0,\"thresholds\":{\"mode\":\"absolute\",\"steps\":[{\"color\":\"green\",\"value\":null},{\"color\":\"red\",\"value\":80}]},\"unit\":\"Bps\"},\"overrides\":[]},\"gridPos\":{\"h\":6,\"w\":12,\"x\":12,\"y\":50},\"id\":78,\"links\":[],\"options\":{\"legend\":{\"calcs\":[],\"displayMode\":\"list\",\"placement\":\"bottom\",\"showLegend\":true},\"tooltip\":{\"mode\":\"multi\",\"sort\":\"none\"}},\"pluginVersion\":\"10.1.5\",\"targets\":[{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_sent_bytes_total{reporter=\\\"source\\\",
    connection_security_policy=\\\"mutual_tls\\\", source_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$workload\\\", destination_service=~\\\"$dstsvc\\\"}[$__rate_interval]))
    by (destination_service), 0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }} (\U0001F510mTLS)\",\"refId\":\"A\",\"step\":2},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"expr\":\"round(sum(irate(istio_tcp_sent_bytes_total{reporter=\\\"source\\\",
    connection_security_policy!=\\\"mutual_tls\\\", source_workload_namespace=~\\\"$namespace\\\",
    source_workload=~\\\"$workload\\\", destination_service=~\\\"$dstsvc\\\"}[$__rate_interval]))
    by (destination_service), 0.001)\",\"format\":\"time_series\",\"intervalFactor\":1,\"legendFormat\":\"{{
    destination_service }}\",\"refId\":\"B\",\"step\":2}],\"title\":\"Bytes Received
    from Outgoing TCP Connection\",\"type\":\"timeseries\"}],\"refresh\":\"1m\",\"schemaVersion\":38,\"style\":\"dark\",\"tags\":[],\"templating\":{\"list\":[{\"hide\":0,\"includeAll\":false,\"multi\":false,\"name\":\"datasource\",\"options\":[],\"query\":\"prometheus\",\"queryValue\":\"\",\"refresh\":1,\"regex\":\"\",\"skipUrlSync\":false,\"type\":\"datasource\"},{\"current\":{\"selected\":true,\"text\":\"default\",\"value\":\"default\"},\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"definition\":\"\",\"hide\":0,\"includeAll\":false,\"label\":\"Namespace\",\"multi\":false,\"name\":\"namespace\",\"options\":[],\"query\":\"query_result(sum(istio_requests_total)
    by (destination_workload_namespace) or sum(istio_tcp_sent_bytes_total) by (destination_workload_namespace))\",\"refresh\":1,\"regex\":\"/.*_namespace=\\\"([^\\\"]*).*/\",\"skipUrlSync\":false,\"sort\":0,\"tagValuesQuery\":\"\",\"tagsQuery\":\"\",\"type\":\"query\",\"useTags\":false},{\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"definition\":\"\",\"hide\":0,\"includeAll\":false,\"label\":\"Workload\",\"multi\":false,\"name\":\"workload\",\"options\":[],\"query\":\"query_result((sum(istio_requests_total{destination_workload_namespace=~\\\"$namespace\\\"})
    by (destination_workload) or sum(istio_requests_total{source_workload_namespace=~\\\"$namespace\\\"})
    by (source_workload)) or (sum(istio_tcp_sent_bytes_total{destination_workload_namespace=~\\\"$namespace\\\"})
    by (destination_workload) or sum(istio_tcp_sent_bytes_total{source_workload_namespace=~\\\"$namespace\\\"})
    by (source_workload)))\",\"refresh\":1,\"regex\":\"/.*workload=\\\"([^\\\"]*).*/\",\"skipUrlSync\":false,\"sort\":1,\"tagValuesQuery\":\"\",\"tagsQuery\":\"\",\"type\":\"query\",\"useTags\":false},{\"current\":{\"selected\":false,\"text\":\"destination\",\"value\":\"destination\"},\"datasource\":\"Prometheus\",\"definition\":\"\",\"hide\":0,\"includeAll\":false,\"label\":\"Reporter\",\"multi\":true,\"name\":\"qrep\",\"options\":[{\"selected\":false,\"text\":\"source\",\"value\":\"source\"},{\"selected\":true,\"text\":\"destination\",\"value\":\"destination\"}],\"query\":\"source,destination\",\"refresh\":1,\"regex\":\"\",\"skipUrlSync\":false,\"sort\":2,\"tagValuesQuery\":\"\",\"tagsQuery\":\"\",\"type\":\"custom\",\"useTags\":false},{\"current\":{\"selected\":false,\"text\":\"All\",\"value\":\"$__all\"},\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"definition\":\"\",\"hide\":0,\"includeAll\":true,\"label\":\"Inbound
    Workload Namespace\",\"multi\":true,\"name\":\"srcns\",\"options\":[],\"query\":\"query_result(sum(istio_requests_total{reporter=~\\\"$qrep\\\",
    destination_workload=\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\"})
    by (source_workload_namespace) or sum(istio_tcp_sent_bytes_total{reporter=~\\\"$qrep\\\",
    destination_workload=\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\"})
    by (source_workload_namespace))\",\"refresh\":1,\"regex\":\"/.*namespace=\\\"([^\\\"]*).*/\",\"skipUrlSync\":false,\"sort\":2,\"tagValuesQuery\":\"\",\"tagsQuery\":\"\",\"type\":\"query\",\"useTags\":false},{\"current\":{\"selected\":false,\"text\":\"All\",\"value\":\"$__all\"},\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"definition\":\"\",\"hide\":0,\"includeAll\":true,\"label\":\"Inbound
    Workload\",\"multi\":true,\"name\":\"srcwl\",\"options\":[],\"query\":\"query_result(sum(istio_requests_total{reporter=~\\\"$qrep\\\",
    destination_workload=\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload_namespace=~\\\"$srcns\\\"}) by (source_workload) or sum(istio_tcp_sent_bytes_total{reporter=~\\\"$qrep\\\",
    destination_workload=\\\"$workload\\\", destination_workload_namespace=~\\\"$namespace\\\",
    source_workload_namespace=~\\\"$srcns\\\"}) by (source_workload))\",\"refresh\":1,\"regex\":\"/.*workload=\\\"([^\\\"]*).*/\",\"skipUrlSync\":false,\"sort\":3,\"tagValuesQuery\":\"\",\"tagsQuery\":\"\",\"type\":\"query\",\"useTags\":false},{\"current\":{\"selected\":false,\"text\":\"All\",\"value\":\"$__all\"},\"datasource\":{\"type\":\"prometheus\",\"uid\":\"${datasource}\"},\"definition\":\"\",\"hide\":0,\"includeAll\":true,\"label\":\"Destination
    Service\",\"multi\":true,\"name\":\"dstsvc\",\"options\":[],\"query\":\"query_result(sum(istio_requests_total{reporter=\\\"source\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\"})
    by (destination_service) or sum(istio_tcp_sent_bytes_total{reporter=\\\"source\\\",
    source_workload=~\\\"$workload\\\", source_workload_namespace=~\\\"$namespace\\\"})
    by (destination_service))\",\"refresh\":1,\"regex\":\"/.*destination_service=\\\"([^\\\"]*).*/\",\"skipUrlSync\":false,\"sort\":4,\"tagValuesQuery\":\"\",\"tagsQuery\":\"\",\"type\":\"query\",\"useTags\":false}]},\"time\":{\"from\":\"now-30m\",\"to\":\"now\"},\"timepicker\":{\"refresh_intervals\":[\"5m\",\"15m\",\"30m\",\"1h\",\"2h\",\"1d\"],\"time_options\":[\"5m\",\"15m\",\"1h\",\"6h\",\"12h\",\"24h\",\"2d\",\"7d\",\"30d\"]},\"timezone\":\"\",\"title\":\"Istio
    Workload Dashboard\",\"version\":1,\"weekStart\":\"\"}\n"
kind: ConfigMap
metadata:
  creationTimestamp: null
  name: istio-services-grafana-dashboards
  namespace: istio-system
