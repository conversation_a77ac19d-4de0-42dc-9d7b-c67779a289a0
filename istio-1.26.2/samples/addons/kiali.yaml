---
# Source: kiali-server/templates/serviceaccount.yaml
apiVersion: v1
kind: ServiceAccount
metadata:
  name: kiali
  namespace: "istio-system"
  labels:
    helm.sh/chart: kiali-server-2.8.0
    app: kiali
    app.kubernetes.io/name: kiali
    app.kubernetes.io/instance: kiali
    version: "v2.8.0"
    app.kubernetes.io/version: "v2.8.0"
    app.kubernetes.io/part-of: "kiali"
...
---
# Source: kiali-server/templates/configmap.yaml
apiVersion: v1
kind: ConfigMap
metadata:
  name: kiali
  namespace: "istio-system"
  labels:
    helm.sh/chart: kiali-server-2.8.0
    app: kiali
    app.kubernetes.io/name: kiali
    app.kubernetes.io/instance: kiali
    version: "v2.8.0"
    app.kubernetes.io/version: "v2.8.0"
    app.kubernetes.io/part-of: "kiali"
data:
  config.yaml: |
    additional_display_details:
    - annotation: kiali.io/api-spec
      icon_annotation: kiali.io/api-type
      title: API Documentation
    auth:
      openid: {}
      openshift:
        client_id_prefix: kiali
      strategy: anonymous
    clustering:
      autodetect_secrets:
        enabled: true
        label: kiali.io/multiCluster=true
      clusters: []
    deployment:
      additional_service_yaml: {}
      affinity:
        node: {}
        pod: {}
        pod_anti: {}
      cluster_wide_access: true
      configmap_annotations: {}
      custom_envs: []
      custom_secrets: []
      dns:
        config: {}
        policy: ""
      host_aliases: []
      hpa:
        api_version: autoscaling/v2
        spec: {}
      image_digest: ""
      image_name: quay.io/kiali/kiali
      image_pull_policy: IfNotPresent
      image_pull_secrets: []
      image_version: v2.8
      ingress:
        additional_labels: {}
        class_name: nginx
        override_yaml:
          metadata: {}
      ingress_enabled: false
      instance_name: kiali
      logger:
        log_format: text
        log_level: info
        sampler_rate: "1"
        time_field_format: 2006-01-02T15:04:05Z07:00
      namespace: istio-system
      node_selector: {}
      pod_annotations: {}
      pod_labels:
        sidecar.istio.io/inject: "false"
      priority_class_name: ""
      probes:
        liveness:
          initial_delay_seconds: 5
          period_seconds: 30
        readiness:
          initial_delay_seconds: 5
          period_seconds: 30
        startup:
          failure_threshold: 6
          initial_delay_seconds: 30
          period_seconds: 10
      remote_cluster_resources_only: false
      replicas: 1
      resources:
        limits:
          memory: 1Gi
        requests:
          cpu: 10m
          memory: 64Mi
      secret_name: kiali
      security_context: {}
      service_annotations: {}
      service_type: ""
      tolerations: []
      topology_spread_constraints: []
      version_label: v2.8.0
      view_only_mode: false
    external_services:
      custom_dashboards:
        enabled: true
      istio:
        root_namespace: istio-system
      tracing:
        enabled: false
    identity:
      cert_file: ""
      private_key_file: ""
    istio_namespace: istio-system
    kiali_feature_flags:
      disabled_features: []
      validations:
        ignore:
        - KIA1301
    login_token:
      signing_key: CHANGEME00000000
    server:
      observability:
        metrics:
          enabled: true
          port: 9090
      port: 20001
      web_root: /kiali
...
---
# Source: kiali-server/templates/role.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: kiali
  labels:
    helm.sh/chart: kiali-server-2.8.0
    app: kiali
    app.kubernetes.io/name: kiali
    app.kubernetes.io/instance: kiali
    version: "v2.8.0"
    app.kubernetes.io/version: "v2.8.0"
    app.kubernetes.io/part-of: "kiali"
rules:
- apiGroups: [""]
  resources:
  - configmaps
  - endpoints
  - pods/log
  verbs:
  - get
  - list
  - watch
- apiGroups: [""]
  resources:
  - namespaces
  - pods
  - replicationcontrollers
  - services
  verbs:
  - get
  - list
  - watch
  - patch
- apiGroups: [""]
  resources:
  - pods/portforward
  verbs:
  - create
  - post
- apiGroups: ["extensions", "apps"]
  resources:
  - daemonsets
  - deployments
  - replicasets
  - statefulsets
  verbs:
  - get
  - list
  - watch
  - patch
- apiGroups: ["batch"]
  resources:
  - cronjobs
  - jobs
  verbs:
  - get
  - list
  - watch
  - patch
- apiGroups:
  - networking.istio.io
  - security.istio.io
  - extensions.istio.io
  - telemetry.istio.io
  - gateway.networking.k8s.io
  resources: ["*"]
  verbs:
  - get
  - list
  - watch
  - create
  - delete
  - patch
- apiGroups: ["apps.openshift.io"]
  resources:
  - deploymentconfigs
  verbs:
  - get
  - list
  - watch
  - patch
- apiGroups: ["project.openshift.io"]
  resources:
  - projects
  verbs:
  - get
- apiGroups: ["route.openshift.io"]
  resources:
  - routes
  verbs:
  - get
- apiGroups: ["authentication.k8s.io"]
  resources:
  - tokenreviews
  verbs:
  - create
- apiGroups: ["oauth.openshift.io"]
  resources:
  - oauthclients
  resourceNames:
  - kiali-istio-system
  verbs:
  - get
- apiGroups: ["admissionregistration.k8s.io"]
  resources:
  - mutatingwebhookconfigurations
  verbs:
  - get
  - list
  - watch
...
---
# Source: kiali-server/templates/rolebinding.yaml
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: kiali
  labels:
    helm.sh/chart: kiali-server-2.8.0
    app: kiali
    app.kubernetes.io/name: kiali
    app.kubernetes.io/instance: kiali
    version: "v2.8.0"
    app.kubernetes.io/version: "v2.8.0"
    app.kubernetes.io/part-of: "kiali"
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: kiali
subjects:
- kind: ServiceAccount
  name: kiali
  namespace: "istio-system"
...
---
# Source: kiali-server/templates/service.yaml
apiVersion: v1
kind: Service
metadata:
  name: kiali
  namespace: "istio-system"
  labels:
    helm.sh/chart: kiali-server-2.8.0
    app: kiali
    app.kubernetes.io/name: kiali
    app.kubernetes.io/instance: kiali
    version: "v2.8.0"
    app.kubernetes.io/version: "v2.8.0"
    app.kubernetes.io/part-of: "kiali"
  annotations:
spec:
  ports:
  - name: http
    appProtocol: http
    protocol: TCP
    port: 20001
  - name: http-metrics
    appProtocol: http
    protocol: TCP
    port: 9090
  selector:
    app.kubernetes.io/name: kiali
    app.kubernetes.io/instance: kiali
...
---
# Source: kiali-server/templates/deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: kiali
  namespace: "istio-system"
  labels:
    helm.sh/chart: kiali-server-2.8.0
    app: kiali
    app.kubernetes.io/name: kiali
    app.kubernetes.io/instance: kiali
    version: "v2.8.0"
    app.kubernetes.io/version: "v2.8.0"
    app.kubernetes.io/part-of: "kiali"
spec:
  replicas: 1
  selector:
    matchLabels:
      app.kubernetes.io/name: kiali
      app.kubernetes.io/instance: kiali
  strategy:
    rollingUpdate:
      maxSurge: 1
      maxUnavailable: 1
    type: RollingUpdate
  template:
    metadata:
      name: kiali
      labels:
        helm.sh/chart: kiali-server-2.8.0
        app: kiali
        app.kubernetes.io/name: kiali
        app.kubernetes.io/instance: kiali
        version: "v2.8.0"
        app.kubernetes.io/version: "v2.8.0"
        app.kubernetes.io/part-of: "kiali"
        sidecar.istio.io/inject: "false"
      annotations:
        checksum/config: bf68d54d762c82a262ec2c717f8acc32f59090f760339eb68831dbd0e4675436
        prometheus.io/scrape: "true"
        prometheus.io/port: "9090"
        kiali.io/dashboards: go,kiali
    spec:
      serviceAccountName: kiali
      containers:
      - image: "quay.io/kiali/kiali:v2.8"
        imagePullPolicy: IfNotPresent
        name: kiali
        command:
        - "/opt/kiali/kiali"
        - "-config"
        - "/kiali-configuration/config.yaml"
        terminationMessagePolicy: FallbackToLogsOnError
        securityContext:
          allowPrivilegeEscalation: false
          privileged: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          capabilities:
            drop:
            - ALL
        ports:
        - name: api-port
          containerPort: 20001
        - name: http-metrics
          containerPort: 9090
        readinessProbe:
          httpGet:
            path: /kiali/healthz
            port: api-port
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 30
        livenessProbe:
          httpGet:
            path: /kiali/healthz
            port: api-port
            scheme: HTTP
          initialDelaySeconds: 5
          periodSeconds: 30
        startupProbe:
          httpGet:
            path: /kiali/healthz
            port: api-port
            scheme: HTTP
          failureThreshold: 6
          initialDelaySeconds: 30
          periodSeconds: 10
        env:
        - name: ACTIVE_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        - name: LOG_LEVEL
          value: "info"
        - name: LOG_FORMAT
          value: "text"
        - name: LOG_TIME_FIELD_FORMAT
          value: "2006-01-02T15:04:05Z07:00"
        - name: LOG_SAMPLER_RATE
          value: "1"
        volumeMounts:
        - name: kiali-configuration
          mountPath: "/kiali-configuration"
        - name: kiali-cert
          mountPath: "/kiali-cert"
        - name: kiali-secret
          mountPath: "/kiali-secret"
        - name: kiali-cabundle
          mountPath: "/kiali-cabundle"
        - name: "kiali-multi-cluster-secret"
          mountPath: "/kiali-remote-cluster-secrets/kiali-multi-cluster-secret"
          readOnly: true
        resources:
          limits:
            memory: 1Gi
          requests:
            cpu: 10m
            memory: 64Mi
      volumes:
      - name: kiali-configuration
        configMap:
          name: kiali
      - name: kiali-cert
        secret:
          secretName: istio.kiali-service-account
          optional: true
      - name: kiali-secret
        secret:
          secretName: kiali
          optional: true
      - name: kiali-cabundle
        configMap:
          name: kiali-cabundle
          optional: true
      - name: "kiali-multi-cluster-secret"
        secret:
          secretName: "kiali-multi-cluster-secret"
          optional: true
...
