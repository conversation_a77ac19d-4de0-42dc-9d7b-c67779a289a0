apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: istio-cni
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  destination:
    name: ambient-cluster
    namespace: kube-system
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
  sources:
    - repoURL: 'https://istio-release.storage.googleapis.com/charts'
      targetRevision: 1.18.5
      helm:
        valuesObject:
          revision: rapid
          cni:
            cniBinDir: "/home/<USER>/bin"
        valueFiles:
          - >-
            $values/manifests/charts/istio-cni/ambient-values.yaml
      chart: cni
    - repoURL: 'https://github.com/istio/istio.git'
      targetRevision: HEAD
      ref: values
