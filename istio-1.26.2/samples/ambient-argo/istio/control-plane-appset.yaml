apiVersion: argoproj.io/v1alpha1
kind: ApplicationSet
metadata:
  name: istio-multi-control
spec:
  generators:
  - list:
      elements:
      - version: 1.18.5
        revision: 1-18-5
      - version: 1.19.3
        revision: 1-19-3
  template:
    metadata:
      name: 'istio-control-{{revision}}'
    spec:
      project: default
      sources:
      - repoURL: 'https://istio-release.storage.googleapis.com/charts'
        targetRevision: '{{version}}'
        helm:
          valuesObject:
            revision: '{{revision}}'
          valueFiles:
            - >-
              $values/manifests/charts/istio-control/istio-discovery/ambient-values.yaml
        chart: istiod
      - repoURL: 'https://github.com/istio/istio.git'
        targetRevision: HEAD
        ref: values
      destination:
        name: ambient-cluster
        namespace: istio-system
      syncPolicy:
        automated:
          prune: true
          selfHeal: true
        syncOptions:
          - CreateNamespace=true