apiVersion: argoproj.io/v1alpha1
kind: Application
metadata:
  name: istio-addons
  namespace: argocd
  finalizers:
    - resources-finalizer.argocd.argoproj.io
spec:
  project: default
  source:
    repoURL: 'https://github.com/istio/istio.git'
    targetRevision: HEAD
    path: samples/addons
    directory:
      exclude: loki.yaml
  destination:
    name: ambient-cluster
    namespace: istio-system
  syncPolicy:
    automated:
      prune: true
      selfHeal: true
    syncOptions:
      - CreateNamespace=true
