apiVersion: v1
kind: Service
metadata:
  name: reviews-v1
spec:
  ports:
  - port: 9080
    name: http
  selector:
    app: reviews
    version: v1
---
apiVersion: v1
kind: Service
metadata:
  name: reviews-v2
spec:
  ports:
  - port: 9080
    name: http
  selector:
    app: reviews
    version: v2
---
apiVersion: v1
kind: Service
metadata:
  name: reviews-v3
spec:
  ports:
  - port: 9080
    name: http
  selector:
    app: reviews
    version: v3
---
apiVersion: v1
kind: Service
metadata:
  name: productpage-v1
spec:
  ports:
  - port: 9080
    name: http
  selector:
    app: productpage
    version: v1
---
apiVersion: v1
kind: Service
metadata:
  name: ratings-v1
spec:
  ports:
  - port: 9080
    name: http
  selector:
    app: ratings
    version: v1
---
apiVersion: v1
kind: Service
metadata:
  name: details-v1
spec:
  ports:
  - port: 9080
    name: http
  selector:
    app: details
    version: v1
---
