# This ServiceEntry exposes the hosts needed for installing packages with apt-get.
# After applying this file, Istio-enabled pods (configured apt-get) be able to execute
# `apt-get upgrade` and `apt-get install`.  If this is not installed you may get
# "404  Not Found"

apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: make-aptget-work
spec:
  hosts:
  - deb.debian.org
  - cdn-fastly.deb.debian.org
  - security.debian.org
  - archive.ubuntu.com
  - security.ubuntu.com
  ports:
  - number: 80
    name: http
    protocol: HTTP
