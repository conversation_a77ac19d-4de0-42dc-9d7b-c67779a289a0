# This ServiceEntry exposes the hosts needed for github.com.
# After applying this file, Istio-enabled pods will be able to execute
# `git clone https://github.com/istio/api.git` and (with local identification
# config and certificate) `<NAME_EMAIL>:istio/api.git`

# HTTP and TLS, the host must be specified
# See https://istio.io/docs/tasks/traffic-management/egress/
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: github-https
spec:
  hosts:
  - github.com
  ports:
  - number: 443
    name: https
    protocol: HTTPS
---
# For TCP services the IP ranges SHOULD be specified to avoid problems
# if multiple SEs use the same port number.
# See https://istio.io/blog/2018/egress-tcp/#mesh-external-service-entry-for-an-external-mysql-instance
apiVersion: networking.istio.io/v1
kind: ServiceEntry
metadata:
  name: github-tcp
spec:
  hosts:
  - dummy.github.com # not used
  addresses: # from https://help.github.com/articles/about-github-s-ip-addresses/
  - "*************/32"
  - "**************/32"
  - "************/20"
  - "*************/32"
  - "************/32"
  - "*************/22"
  - "***************/32"
  - "***************/32"
  - "***************/32"
  - "***************/32"
  - "************/22"
  - "**************/32"
  - "**************/32"
  - "**********/32"
  - "************/32"
  - "*************/32"
  - "************/32"
  - "***********/32"
  ports:
  - name: tcp
    number: 22
    protocol: tcp
  location: MESH_EXTERNAL
