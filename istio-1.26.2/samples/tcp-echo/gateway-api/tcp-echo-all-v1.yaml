apiVersion: gateway.networking.k8s.io/v1
kind: Gateway
metadata:
  name: tcp-echo-gateway
spec:
  gatewayClassName: istio
  listeners:
  - name: tcp-31400
    protocol: TCP
    port: 31400
    allowedRoutes:
      kinds:
      - kind: TCPRoute
---
apiVersion: v1
kind: Service
metadata:
  name: tcp-echo-v1
spec:
  ports:
  - port: 9000
    name: tcp
  selector:
    app: tcp-echo
    version: v1
---
apiVersion: v1
kind: Service
metadata:
  name: tcp-echo-v2
spec:
  ports:
  - port: 9000
    name: tcp
  selector:
    app: tcp-echo
    version: v2
---
apiVersion: gateway.networking.k8s.io/v1alpha2
kind: TCPRoute
metadata:
  name: tcp-echo
spec:
  parentRefs:
  - name: tcp-echo-gateway
    sectionName: tcp-31400
  rules:
  - backendRefs:
    - name: tcp-echo-v1
      port: 9000
