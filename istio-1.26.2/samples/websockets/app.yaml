apiVersion: v1
kind: Service
metadata:
  name: tornado
  labels:
    app: tornado
    service: tornado
spec:
  ports:
  - port: 8888
    name: http
  selector:
    app: tornado
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: tornado
spec:
  replicas: 1
  selector:
    matchLabels:
      app: tornado
      version: v1
  template:
    metadata:
      labels:
        app: tornado
        version: v1
    spec:
      containers:
      - name: tornado
        image: hiroakis/tornado-websocket-example
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8888
---
