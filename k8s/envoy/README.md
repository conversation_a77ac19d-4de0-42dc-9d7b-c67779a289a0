# Envoy Configuration

This directory contains the Envoy proxy configuration for the k8s-envoy POC system.

## File Structure

```
k8s/envoy/
├── envoy-config.yaml    # Pure Envoy configuration (edit this file)
├── configmap.yaml       # Kubernetes ConfigMap template (auto-generated)
└── README.md           # This documentation
```

## Configuration Files

### `envoy-config.yaml`
- **Purpose**: Contains the pure Envoy proxy configuration
- **Format**: Standard Envoy YAML configuration
- **Editing**: This is the file you should edit when making changes to Envoy configuration
- **Features**:
  - Inbound listener (port 15006) with JWT validation
  - Outbound listener (port 15001) with traffic routing
  - Admin interface (port 15000)
  - Service clusters for local service, Apisix gateway, and external services

### `configmap.yaml`
- **Purpose**: Kubernetes ConfigMap template
- **Status**: Auto-generated during deployment
- **Note**: Do not edit this file directly - it's overwritten during deployment

## Key Configuration Features

### Inbound Traffic (Port 15006)
- **JWT Validation**: Validates ServiceAccount JWT tokens from Apisix
- **Authentication**: Rejects requests without valid JWT tokens
- **Routing**: Forwards validated requests to local service (port 8010)

### Outbound Traffic (Port 15001)
- **Internal Routing**: Routes internal k8s traffic through Apisix gateway
- **External Access**: Direct routing to external services with domain whitelist
- **JWT Injection**: Adds ServiceAccount JWT token to outbound requests
- **Domain Control**: Restricts external access to approved domains

### Clusters
- **local_service**: Routes to the local application (127.0.0.1:8010)
- **apisix_gateway**: Routes to Apisix for internal service communication
- **external_cluster**: Routes to external internet services (with TLS)

## Making Changes

### To modify Envoy configuration:

1. **Edit the configuration**:
   ```bash
   vim k8s/envoy/envoy-config.yaml
   ```

2. **Redeploy the system**:
   ```bash
   scripts/deploy/deploy-system.sh
   ```

3. **Verify the changes**:
   ```bash
   kubectl get configmap envoy-config -o yaml
   kubectl logs -l app=trading-service | grep envoy
   ```

### Common Configuration Changes

#### Adding External Domains
Edit the `allowed_external_domains` table in the outbound Lua filter:
```lua
local allowed_external_domains = {
  ["httpbin.org"] = true,
  ["api.github.com"] = true,
  ["jsonplaceholder.typicode.com"] = true,
  ["your-new-domain.com"] = true  -- Add new domain here
}
```

#### Changing Service Ports
Update the `local_service` cluster configuration:
```yaml
- name: local_service
  # ... other config ...
  load_assignment:
    cluster_name: local_service
    endpoints:
    - lb_endpoints:
      - endpoint:
          address:
            socket_address:
              address: 127.0.0.1
              port_value: 8010  # Change this port
```

#### Modifying JWT Validation
Edit the inbound Lua filter to change JWT validation logic:
```lua
-- JWT validation: only accept requests from Apisix (with valid JWT)
local jwt_token = request_handle:headers():get("authorization")
-- Add your custom validation logic here
```

## Deployment Process

The deployment script (`scripts/deploy/deploy-system.sh`) automatically:

1. **Generates ConfigMap**: Creates the Kubernetes ConfigMap from `envoy-config.yaml`
2. **Applies Configuration**: Deploys the ConfigMap to the cluster
3. **Updates Pods**: Restarts pods to pick up the new configuration

## Troubleshooting

### Check ConfigMap Content
```bash
kubectl get configmap envoy-config -o yaml
```

### View Envoy Logs
```bash
kubectl logs -l app=trading-service -c envoy-sidecar
kubectl logs -l app=asset-service -c envoy-sidecar
```

### Test Configuration
```bash
# Test internal routing
kubectl exec -it deployment/trading-service -- curl -H "Authorization: Bearer $(cat /var/run/secrets/kubernetes.io/serviceaccount/token)" http://apisix-gateway/asset/health

# Test external access
kubectl exec -it deployment/trading-service -- curl https://httpbin.org/get
```

## Benefits of This Structure

✅ **Better Readability**: Clean separation of Envoy config from Kubernetes resources  
✅ **Syntax Highlighting**: Proper YAML highlighting for Envoy configuration  
✅ **Version Control**: Cleaner diffs when making configuration changes  
✅ **Easier Maintenance**: Direct editing of Envoy config without YAML escaping  
✅ **ConfigMap Deployment**: Still uses ConfigMaps as requested for deployment
