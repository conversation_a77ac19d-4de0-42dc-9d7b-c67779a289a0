# Envoy Configuration

This directory contains the Envoy proxy configuration for the k8s-envoy POC system.

## File Structure

```
k8s/envoy/
├── envoy-config.yaml    # Pure Envoy configuration (references external Lua files)
├── lua/
│   ├── inbound-filter.lua   # Inbound JWT validation Lua filter
│   └── outbound-filter.lua  # Outbound routing and JWT injection Lua filter
├── configmap.yaml       # Kubernetes ConfigMap template (auto-generated)
└── README.md           # This documentation
```

## Configuration Files

### `envoy-config.yaml`
- **Purpose**: Contains the pure Envoy proxy configuration structure
- **Format**: Standard Envoy YAML configuration with references to external Lua files
- **Editing**: Edit this file for structural changes to Envoy configuration
- **Features**:
  - Inbound listener (port 15006) with JWT validation (references inbound-filter.lua)
  - Outbound listener (port 15001) with traffic routing (references outbound-filter.lua)
  - Admin interface (port 15000)
  - Service clusters for local service, Apisix gateway, and external services

### `lua/inbound-filter.lua`
- **Purpose**: Handles JWT validation for incoming requests from Apisix
- **Features**:
  - ServiceAccount JWT token validation
  - Bearer token format checking
  - JWT structure validation (header.payload.signature)
  - Request authentication and authorization

### `lua/outbound-filter.lua`
- **Purpose**: Handles outbound request processing and routing
- **Features**:
  - ServiceAccount JWT token injection for authentication
  - Internal traffic routing through Apisix gateway
  - External domain whitelist enforcement
  - Request logging and monitoring

### `configmap.yaml`
- **Purpose**: Kubernetes ConfigMap template
- **Status**: Auto-generated during deployment
- **Note**: Do not edit this file directly - it's overwritten during deployment

## Key Configuration Features

### Inbound Traffic (Port 15006)
- **JWT Validation**: Validates ServiceAccount JWT tokens from Apisix
- **Authentication**: Rejects requests without valid JWT tokens
- **Routing**: Forwards validated requests to local service (port 8010)

### Outbound Traffic (Port 15001)
- **Internal Routing**: Routes internal k8s traffic through Apisix gateway
- **External Access**: Direct routing to external services with domain whitelist
- **JWT Injection**: Adds ServiceAccount JWT token to outbound requests
- **Domain Control**: Restricts external access to approved domains

### Clusters
- **local_service**: Routes to the local application (127.0.0.1:8010)
- **apisix_gateway**: Routes to Apisix for internal service communication
- **external_cluster**: Routes to external internet services (with TLS)

## Making Changes

### To modify Envoy configuration:

1. **Edit the configuration files**:
   ```bash
   # For structural changes (listeners, clusters, etc.)
   vim k8s/envoy/envoy-config.yaml

   # For inbound JWT validation logic
   vim k8s/envoy/lua/inbound-filter.lua

   # For outbound routing and external access control
   vim k8s/envoy/lua/outbound-filter.lua
   ```

2. **Redeploy the system**:
   ```bash
   scripts/deploy/deploy-system.sh
   ```

3. **Verify the changes**:
   ```bash
   kubectl get configmap envoy-config -o yaml
   kubectl logs -l app=trading-service | grep envoy
   ```

### Common Configuration Changes

#### Adding External Domains
Edit the `allowed_external_domains` table in `k8s/envoy/lua/outbound-filter.lua`:
```lua
local allowed_external_domains = {
  ["httpbin.org"] = true,
  ["api.github.com"] = true,
  ["jsonplaceholder.typicode.com"] = true,
  ["your-new-domain.com"] = true  -- Add new domain here
}
```

#### Changing Service Ports
Update the `local_service` cluster configuration:
```yaml
- name: local_service
  # ... other config ...
  load_assignment:
    cluster_name: local_service
    endpoints:
    - lb_endpoints:
      - endpoint:
          address:
            socket_address:
              address: 127.0.0.1
              port_value: 8010  # Change this port
```

#### Modifying JWT Validation
Edit `k8s/envoy/lua/inbound-filter.lua` to change JWT validation logic:
```lua
-- JWT validation: only accept requests from Apisix (with valid JWT)
local jwt_token = request_handle:headers():get("authorization")
-- Add your custom validation logic here
```

#### Modifying Outbound Request Processing
Edit `k8s/envoy/lua/outbound-filter.lua` to change routing or authentication logic:
```lua
-- Add ServiceAccount JWT token for authentication
local jwt_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
-- Modify JWT injection or routing logic here
```

## Deployment Process

The deployment script (`scripts/deploy/deploy-system.sh`) automatically:

1. **Embeds Lua Code**: Uses `scripts/deploy/embed-lua.py` to embed external Lua files into the config
2. **Generates ConfigMap**: Creates the Kubernetes ConfigMap from the processed configuration
3. **Applies Configuration**: Deploys the ConfigMap to the cluster
4. **Updates Pods**: Restarts pods to pick up the new configuration

## Troubleshooting

### Check ConfigMap Content
```bash
kubectl get configmap envoy-config -o yaml
```

### View Envoy Logs
```bash
kubectl logs -l app=trading-service -c envoy-sidecar
kubectl logs -l app=asset-service -c envoy-sidecar
```

### Test Configuration
```bash
# Test internal routing
kubectl exec -it deployment/trading-service -- curl -H "Authorization: Bearer $(cat /var/run/secrets/kubernetes.io/serviceaccount/token)" http://apisix-gateway/asset/health

# Test external access
kubectl exec -it deployment/trading-service -- curl https://httpbin.org/get
```

## Benefits of This Structure

✅ **Better Readability**: Clean separation of Envoy config from Kubernetes resources
✅ **Syntax Highlighting**: Proper YAML highlighting for Envoy config and Lua syntax highlighting for filters
✅ **Version Control**: Cleaner diffs when making configuration changes
✅ **Easier Maintenance**: Direct editing of Envoy config and Lua filters without YAML escaping
✅ **Modular Code**: Separate Lua files for different filter responsibilities
✅ **ConfigMap Deployment**: Still uses ConfigMaps as requested for deployment
✅ **Automated Processing**: Deployment script automatically embeds Lua code during build
