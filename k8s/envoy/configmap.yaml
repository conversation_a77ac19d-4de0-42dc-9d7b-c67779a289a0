apiVersion: v1
kind: ConfigMap
metadata:
  name: envoy-config
  namespace: default
data:
  envoy.yaml: |
    admin:
      address:
        socket_address:
          address: 0.0.0.0
          port_value: 15000

    static_resources:
      listeners:
      # Inbound listener
      - name: inbound_listener
        address:
          socket_address:
            address: 0.0.0.0
            port_value: 15006
        filter_chains:
        - filters:
          - name: envoy.filters.network.http_connection_manager
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
              stat_prefix: inbound_http
              codec_type: AUTO
              route_config:
                name: inbound_route
                virtual_hosts:
                - name: inbound_service
                  domains: ["*"]
                  routes:
                  - match:
                      prefix: "/"
                    route:
                      cluster: local_service
              http_filters:
              - name: envoy.filters.http.lua
                typed_config:
                  "@type": type.googleapis.com/envoy.extensions.filters.http.lua.v3.Lua
                  inline_code: |
                    function envoy_on_request(request_handle)
                      request_handle:headers():add("x-envoy-inbound", "true")
                      local path = request_handle:headers():get(":path")
                      request_handle:logInfo("Inbound request: " .. path)

                      -- JWT validation: only accept requests from Apisix (with valid JWT)
                      local jwt_token = request_handle:headers():get("authorization")
                      if not jwt_token then
                        request_handle:logWarn("Rejected inbound request without JWT token: " .. path)
                        request_handle:respond(
                          {[":status"] = "401"},
                          "Unauthorized: Missing JWT token"
                        )
                        return
                      end

                      -- Basic JWT format validation (Bearer token)
                      if not jwt_token:match("^Bearer%s+[%w%-_%.]+$") then
                        request_handle:logWarn("Rejected inbound request with invalid JWT format: " .. path)
                        request_handle:respond(
                          {[":status"] = "401"},
                          "Unauthorized: Invalid JWT token format"
                        )
                        return
                      end

                      -- Extract and validate JWT payload (basic validation)
                      local token_part = jwt_token:match("Bearer%s+([%w%-_%.]+)")
                      if token_part then
                        local parts = {}
                        for part in token_part:gmatch("[^%.]+") do
                          table.insert(parts, part)
                        end

                        if #parts ~= 3 then
                          request_handle:logWarn("Rejected inbound request with malformed JWT: " .. path)
                          request_handle:respond(
                            {[":status"] = "401"},
                            "Unauthorized: Malformed JWT token"
                          )
                          return
                        end

                        request_handle:logInfo("Accepted inbound request with valid JWT from Apisix: " .. path)
                      else
                        request_handle:logWarn("Rejected inbound request with unparseable JWT: " .. path)
                        request_handle:respond(
                          {[":status"] = "401"},
                          "Unauthorized: Unparseable JWT token"
                        )
                        return
                      end
                    end
              - name: envoy.filters.http.router
                typed_config:
                  "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router

      # Outbound listener
      - name: outbound_listener
        address:
          socket_address:
            address: 0.0.0.0
            port_value: 15001
        filter_chains:
        - filters:
          - name: envoy.filters.network.http_connection_manager
            typed_config:
              "@type": type.googleapis.com/envoy.extensions.filters.network.http_connection_manager.v3.HttpConnectionManager
              stat_prefix: outbound_http
              codec_type: AUTO
              route_config:
                name: outbound_route
                virtual_hosts:
                - name: apisix_gateway
                  domains: ["apisix-gateway", "apisix-gateway.default.svc.cluster.local"]
                  routes:
                  - match:
                      prefix: "/"
                    route:
                      cluster: apisix_gateway
                - name: external_services
                  domains: ["*"]
                  routes:
                  - match:
                      prefix: "/"
                    route:
                      cluster: external_cluster
              http_filters:
              - name: envoy.filters.http.lua
                typed_config:
                  "@type": type.googleapis.com/envoy.extensions.filters.http.lua.v3.Lua
                  inline_code: |
                    function envoy_on_request(request_handle)
                      local host = request_handle:headers():get(":authority")
                      local path = request_handle:headers():get(":path")
                      
                      request_handle:logInfo("Outbound request to: " .. host .. path)
                      request_handle:headers():add("x-envoy-outbound", "true")

                      -- Add ServiceAccount JWT token for authentication
                      local jwt_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
                      local jwt_token = ""
                      local file = io.open(jwt_token_file, "r")
                      if file then
                        jwt_token = file:read("*all")
                        file:close()
                        if jwt_token and jwt_token ~= "" then
                          request_handle:headers():add("authorization", "Bearer " .. jwt_token:gsub("%s+", ""))
                          request_handle:logInfo("Added ServiceAccount JWT token to request")
                        end
                      else
                        request_handle:logWarn("Could not read ServiceAccount token file")
                      end

                      -- Legacy system routing: internal traffic goes through Apisix, external traffic is direct
                      local is_apisix_request = (host == "apisix-gateway" or host == "apisix-gateway.default.svc.cluster.local")

                      if is_apisix_request then
                        request_handle:logInfo("Routing internal request through Apisix: " .. host .. path)
                      else
                        -- External traffic - apply domain whitelist
                        local allowed_external_domains = {
                          ["httpbin.org"] = true,
                          ["api.github.com"] = true,
                          ["jsonplaceholder.typicode.com"] = true
                        }

                        if not allowed_external_domains[host] then
                          request_handle:logWarn("Blocked request to unauthorized external domain: " .. host)
                          request_handle:respond(
                            {[":status"] = "403"},
                            "Access to external domain " .. host .. " is not allowed"
                          )
                        else
                          request_handle:logInfo("Allowing external request to: " .. host .. path)
                        end
                      end
                    end
              - name: envoy.filters.http.router
                typed_config:
                  "@type": type.googleapis.com/envoy.extensions.filters.http.router.v3.Router

      clusters:
      # Local service cluster
      - name: local_service
        connect_timeout: 0.25s
        type: STATIC
        lb_policy: ROUND_ROBIN
        load_assignment:
          cluster_name: local_service
          endpoints:
          - lb_endpoints:
            - endpoint:
                address:
                  socket_address:
                    address: 127.0.0.1
                    port_value: 8080

      # Apisix gateway cluster - central router for all internal traffic
      - name: apisix_gateway
        connect_timeout: 0.25s
        type: STRICT_DNS
        lb_policy: ROUND_ROBIN
        load_assignment:
          cluster_name: apisix_gateway
          endpoints:
          - lb_endpoints:
            - endpoint:
                address:
                  socket_address:
                    address: apisix-gateway.default.svc.cluster.local
                    port_value: 9080

      # External services cluster
      - name: external_cluster
        connect_timeout: 5s
        type: LOGICAL_DNS
        lb_policy: ROUND_ROBIN
        load_assignment:
          cluster_name: external_cluster
          endpoints:
          - lb_endpoints:
            - endpoint:
                address:
                  socket_address:
                    address: httpbin.org
                    port_value: 443
        transport_socket:
          name: envoy.transport_sockets.tls
          typed_config:
            "@type": type.googleapis.com/envoy.extensions.transport_sockets.tls.v3.UpstreamTlsContext
            sni: httpbin.org
