-- Envoy Inbound <PERSON><PERSON> Filter
-- Handles JWT validation for incoming requests from Apisix
-- This filter ensures only authenticated requests with valid ServiceAccount JWT tokens are processed

function envoy_on_request(request_handle)
  request_handle:headers():add("x-envoy-inbound", "true")
  local path = request_handle:headers():get(":path")
  request_handle:logInfo("Inbound request: " .. path)

  -- JWT validation: only accept requests from Apisix (with valid JWT)
  local jwt_token = request_handle:headers():get("X-ServiceAccount-JWT")
  if not jwt_token then
    request_handle:logWarn("Rejected inbound request without JWT token: " .. path)
    request_handle:respond(
      {[":status"] = "401"},
      "Unauthorized: Missing JWT token"
    )
    return
  end
  local service_name = jwt_token
  if service_name ~= "apisix-gateway" then
    request_handle:logWarn("Rejected inbound request from unauthorized service: " .. service_name)
    request_handle:respond(
      {[":status"] = "401"},
      "Unauthorized: Rejected request from unauthorized service"
    )
    return
  end
end
