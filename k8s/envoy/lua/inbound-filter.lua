-- Envoy Inbound <PERSON><PERSON> Filter
-- Handles JWT validation for incoming requests from Apisix
-- This filter ensures only authenticated requests with valid ServiceAccount JWT tokens are processed

function envoy_on_request(request_handle)
  request_handle:headers():add("x-envoy-inbound", "true")
  local path = request_handle:headers():get(":path")
  request_handle:logInfo("Inbound request: " .. path)

  -- JWT validation: only accept requests from Apisix (with valid JWT)
  local jwt_token = request_handle:headers():get("X-ServiceAccount-JWT")
  if not jwt_token then
    request_handle:logWarn("Rejected inbound request without JWT token: " .. path)
    request_handle:respond(
      {[":status"] = "401"},
      "Unauthorized: Missing JWT token"
    )
    return
  end

  -- Basic JWT format validation (Bearer token)
  if not jwt_token:match("^Bearer%s+[%w%-_%.]+$") then
    request_handle:logWarn("Rejected inbound request with invalid JWT format: " .. path)
    request_handle:respond(
      {[":status"] = "401"},
      "Unauthorized: Invalid JWT token format"
    )
    return
  end

  -- Extract and validate JWT payload (basic validation)
  local token_part = jwt_token:match("Bearer%s+([%w%-_%.]+)")
  if token_part then
    local parts = {}
    for part in token_part:gmatch("[^%.]+") do
      table.insert(parts, part)
    end

    if #parts ~= 3 then
      request_handle:logWarn("Rejected inbound request with malformed JWT: " .. path)
      request_handle:respond(
        {[":status"] = "401"},
        "Unauthorized: Malformed JWT token"
      )
      return
    end

    request_handle:logInfo("Accepted inbound request with valid JWT from Apisix: " .. path)
  else
    request_handle:logWarn("Rejected inbound request with unparseable JWT: " .. path)
    request_handle:respond(
      {[":status"] = "401"},
      "Unauthorized: Unparseable JWT token"
    )
    return
  end
end
