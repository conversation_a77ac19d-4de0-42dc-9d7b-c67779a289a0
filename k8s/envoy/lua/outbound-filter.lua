-- Envoy Outbound <PERSON><PERSON> Filter
-- <PERSON>les ServiceAccount JWT injection and external domain filtering for outgoing requests
-- Routes internal traffic through Apisix and controls external internet access

function envoy_on_request(request_handle)
  local host = request_handle:headers():get(":authority")
  local path = request_handle:headers():get(":path")
  
  request_handle:logInfo("Outbound request to: " .. host .. path)
  request_handle:headers():add("x-envoy-outbound", "true")

  -- Legacy system routing: internal traffic goes through Apisix, external traffic is direct
  local is_apisix_request = (host == "apisix-gateway" or host == "apisix-gateway.default.svc.cluster.local")

  if is_apisix_request then
    request_handle:logInfo("Routing internal request through Apisix: " .. host .. path)
    -- get SERVICE_NAME from environment variable
    local service_name = os.getenv("SERVICE_NAME")
    request_handle:headers():add("X-ServiceAccount-JWT", service_name)
  else
    -- External traffic - apply domain whitelist
    local allowed_external_domains = {
      ["httpbin.org"] = true,
      ["api.github.com"] = true,
    }

    if not allowed_external_domains[host] then
      request_handle:logWarn("Blocked request to unauthorized external domain: " .. host)
      request_handle:respond(
        {[":status"] = "403"},
        "Access to external domain " .. host .. " is not allowed"
      )
    else
      request_handle:logInfo("Allowing external request to: " .. host .. path)
    end
  end
end
