-- Envoy Outbound <PERSON><PERSON> Filter
-- Handles ServiceAccount JWT injection and external domain filtering for outgoing requests
-- Routes internal traffic through Apisix and controls external internet access

function envoy_on_request(request_handle)
  local host = request_handle:headers():get(":authority")
  local path = request_handle:headers():get(":path")
  
  request_handle:logInfo("Outbound request to: " .. host .. path)
  request_handle:headers():add("x-envoy-outbound", "true")

  -- Add ServiceAccount JWT token for authentication
  local jwt_token_file = "/var/run/secrets/kubernetes.io/serviceaccount/token"
  local jwt_token = ""
  local file = io.open(jwt_token_file, "r")
  if file then
    jwt_token = file:read("*all")
    file:close()
    if jwt_token and jwt_token ~= "" then
      request_handle:headers():add("authorization", "Bearer " .. jwt_token:gsub("%s+", ""))
      request_handle:logInfo("Added ServiceAccount JWT token to request")
    end
  else
    request_handle:logWarn("Could not read ServiceAccount token file")
  end

  -- Legacy system routing: internal traffic goes through Apisix, external traffic is direct
  local is_apisix_request = (host == "apisix-gateway" or host == "apisix-gateway.default.svc.cluster.local")

  if is_apisix_request then
    request_handle:logInfo("Routing internal request through Apisix: " .. host .. path)
  else
    -- External traffic - apply domain whitelist
    local allowed_external_domains = {
      ["httpbin.org"] = true,
      ["api.github.com"] = true,
      ["jsonplaceholder.typicode.com"] = true
    }

    if not allowed_external_domains[host] then
      request_handle:logWarn("Blocked request to unauthorized external domain: " .. host)
      request_handle:respond(
        {[":status"] = "403"},
        "Access to external domain " .. host .. " is not allowed"
      )
    else
      request_handle:logInfo("Allowing external request to: " .. host .. path)
    end
  end
end
