apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: trading-service
  namespace: default
spec:
  hosts:
  - trading-service.default.svc.cluster.local
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: trading-service.default.svc.cluster.local
        subset: v1
        port:
          number: 8080
    timeout: 30s
    retries:
      attempts: 3
      perTryTimeout: 10s
---
apiVersion: networking.istio.io/v1beta1
kind: VirtualService
metadata:
  name: asset-service
  namespace: default
spec:
  hosts:
  - asset-service.default.svc.cluster.local
  http:
  - match:
    - uri:
        prefix: /
    route:
    - destination:
        host: asset-service.default.svc.cluster.local
        subset: v1
        port:
          number: 8080
    timeout: 30s
    retries:
      attempts: 3
      perTryTimeout: 10s
