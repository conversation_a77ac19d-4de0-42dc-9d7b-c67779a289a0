apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: trading-service-authz
  namespace: default
spec:
  selector:
    matchLabels:
      app: trading-service
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/default/sa/apisix-gateway"]
    to:
    - operation:
        methods: ["GET", "POST"]
        paths: ["/health", "/trades", "/trades/*", "/stats"]
  - from:
    - source:
        principals: ["cluster.local/ns/default/sa/asset-service"]
    to:
    - operation:
        methods: ["GET"]
        paths: ["/health"]
---
apiVersion: security.istio.io/v1beta1
kind: AuthorizationPolicy
metadata:
  name: asset-service-authz
  namespace: default
spec:
  selector:
    matchLabels:
      app: asset-service
  rules:
  - from:
    - source:
        principals: ["cluster.local/ns/default/sa/apisix-gateway"]
    to:
    - operation:
        methods: ["GET", "POST", "PUT"]
        paths: ["/health", "/assets", "/assets/*", "/stats"]
  - from:
    - source:
        principals: ["cluster.local/ns/default/sa/trading-service"]
    to:
    - operation:
        methods: ["GET"]
        paths: ["/assets/*"]
