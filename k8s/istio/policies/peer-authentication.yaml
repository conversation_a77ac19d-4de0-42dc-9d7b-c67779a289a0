apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: default
  namespace: default
spec:
  mtls:
    mode: STRICT
---
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: trading-service
  namespace: default
spec:
  selector:
    matchLabels:
      app: trading-service
  mtls:
    mode: STRICT
---
apiVersion: security.istio.io/v1beta1
kind: PeerAuthentication
metadata:
  name: asset-service
  namespace: default
spec:
  selector:
    matchLabels:
      app: asset-service
  mtls:
    mode: STRICT
