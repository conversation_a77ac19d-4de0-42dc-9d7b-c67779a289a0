apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-access-control
  namespace: default
data:
  access-control.lua: |
    -- Access Control Policies for Legacy System
    local access_control = {}
    
    -- Define access control matrix: which service can access what URIs
    local access_policies = {
      -- Trading service access policies
      ["trading-service"] = {
        ["/asset/prices"] = true,           -- Can get asset prices
        ["/asset/market-data"] = true,      -- Can get market data
        ["/asset/.*"] = false,              -- Cannot access other asset endpoints
        ["/trading/.*"] = true,             -- Can access all trading endpoints
      },
      
      -- Asset service access policies  
      ["asset-service"] = {
        ["/trading/orders"] = true,         -- Can access order information
        ["/trading/positions"] = true,      -- Can access position data
        ["/trading/.*"] = false,            -- Cannot access other trading endpoints
        ["/asset/.*"] = true,               -- Can access all asset endpoints
      },
      
      -- Default deny policy for unknown services
      ["default"] = {
        ["/.*"] = false,                    -- Deny all by default
      }
    }
    
    function access_control.check_access(service_name, uri_path)
      -- Get policies for the service, fallback to default
      local service_policies = access_policies[service_name] or access_policies["default"]
      
      -- Check each policy pattern
      for pattern, allowed in pairs(service_policies) do
        if string.match(uri_path, pattern) then
          return allowed
        end
      end
      
      -- Default deny if no pattern matches
      return false
    end
    
    function access_control.extract_service_from_jwt(jwt_token)
      -- Extract service name from JWT token
      -- This is a simplified implementation - in production you'd properly decode the JWT
      if not jwt_token then
        return nil
      end
      
      -- Remove "Bearer " prefix
      local token = jwt_token:gsub("^Bearer%s+", "")
      
      -- For ServiceAccount tokens, the service name is typically in the subject
      -- This is a simplified extraction - in production you'd decode the JWT properly
      local parts = {}
      for part in token:gmatch("[^%.]+") do
        table.insert(parts, part)
      end
      
      if #parts >= 2 then
        -- Try to extract service name from token payload (base64 decoded)
        -- For now, we'll use a simple heuristic based on the pod making the request
        -- In production, you'd properly decode the JWT and extract the service account name
        return "unknown-service"
      end
      
      return nil
    end
    
    return access_control

  policy-matrix.yaml: |
    # Access Control Policy Matrix for Legacy System
    #
    # Format: [source_service] -> [target_endpoint] = [allowed]
    #
    # Trading Service Access Rights:
    trading-service:
      # Can access all trading endpoints (self-access)
      - pattern: "/trading/.*"
        allowed: true
        description: "Full access to trading service endpoints"

      # Can access specific asset endpoints for trading operations
      - pattern: "/asset/prices"
        allowed: true
        description: "Read current asset prices for trading decisions"

      - pattern: "/asset/market-data"
        allowed: true
        description: "Read market data for trading analysis"

      - pattern: "/asset/assets/[A-Z]+"
        allowed: true
        description: "Read specific asset information by symbol"

      # Denied access to asset management endpoints
      - pattern: "/asset/assets$"
        allowed: false
        description: "Cannot list all assets"

      - pattern: "/asset/assets/.*/price"
        allowed: false
        description: "Cannot update asset prices"

    # Asset Service Access Rights:
    asset-service:
      # Can access all asset endpoints (self-access)
      - pattern: "/asset/.*"
        allowed: true
        description: "Full access to asset service endpoints"

      # Can access specific trading endpoints for position tracking
      - pattern: "/trading/trades"
        allowed: true
        description: "Read trades for position calculations"

      - pattern: "/trading/trades/symbol/.*"
        allowed: true
        description: "Read trades by symbol for asset analysis"

      - pattern: "/trading/stats"
        allowed: true
        description: "Read trading statistics for market analysis"

      # Denied access to trade creation/modification
      - pattern: "/trading/trades$"
        allowed: false
        method: "POST"
        description: "Cannot create new trades"

    # Default policy for unknown services
    default:
      - pattern: "/.*"
        allowed: false
        description: "Deny all access by default"

  enforcement-rules.yaml: |
    # Enforcement Rules for Access Control
    #
    # 1. JWT Token Validation:
    #    - All requests must include valid ServiceAccount JWT
    #    - JWT must be in Authorization header as "Bearer <token>"
    #    - Token format must be valid (3 parts separated by dots)
    #
    # 2. Service Identity Extraction:
    #    - Service name extracted from JWT payload
    #    - Fallback to pod labels if JWT parsing fails
    #    - Unknown services get "default" policy
    #
    # 3. URI Pattern Matching:
    #    - Patterns matched in order of definition
    #    - First match determines access decision
    #    - Regex patterns supported for flexible matching
    #
    # 4. HTTP Method Consideration:
    #    - If method specified in policy, must match exactly
    #    - If no method specified, applies to all methods
    #
    # 5. Logging and Monitoring:
    #    - All access decisions logged
    #    - Denied requests logged with reason
    #    - Metrics collected for monitoring
    #
    # 6. Error Handling:
    #    - Invalid JWT: 401 Unauthorized
    #    - Access denied: 403 Forbidden
    #    - Service unavailable: 503 Service Unavailable
