apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-config
  namespace: default
data:
  config.yaml: |
    apisix:
      node_listen: 9080
      enable_ipv6: false
      enable_control: true
      control:
        ip: "0.0.0.0"
        port: 9092
    
    deployment:
      admin:
        admin_key_required: false
        enable_admin_cors: true
        admin_listen:
          ip: 0.0.0.0
          port: 9180
      etcd:
        host:
          - "http://127.0.0.1:2379"
        prefix: "/apisix"
        timeout: 30
    
    nginx_config:
      error_log: "/dev/stderr"
      error_log_level: "warn"
      worker_processes: "auto"
      enable_reuseport: true

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-routes
  namespace: default
data:
  apisix.yaml: |
    routes:
      - id: trading-service-route
        uri: /api/trading/*
        upstream:
          type: roundrobin
          nodes:
            "trading-service.default.svc.cluster.local:8080": 1
        plugins:
          proxy-rewrite:
            regex_uri: ["^/api/trading/(.*)", "/$1"]
          limit-req:
            rate: 10
            burst: 20
            rejected_code: 429
          prometheus:
            {}
      
      - id: asset-service-route
        uri: /api/assets/*
        upstream:
          type: roundrobin
          nodes:
            "asset-service.default.svc.cluster.local:8080": 1
        plugins:
          proxy-rewrite:
            regex_uri: ["^/api/assets/(.*)", "/$1"]
          limit-req:
            rate: 20
            burst: 40
            rejected_code: 429
          prometheus:
            {}

    
    upstreams: []
    
    global_plugins:
      prometheus:
        {}
      cors:
        allow_origins: "*"
        allow_methods: "GET,POST,PUT,DELETE,OPTIONS"
        allow_headers: "Content-Type,Authorization"
        max_age: 86400
    
    plugin_metadata: []
