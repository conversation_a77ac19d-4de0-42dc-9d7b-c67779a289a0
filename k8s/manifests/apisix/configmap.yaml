apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-config
  namespace: default
data:
  config.yaml: |
    apisix:
      node_listen: 80
      enable_ipv6: false
      enable_control: true
      control:
        ip: "0.0.0.0"
        port: 9092
    
    deployment:
      role: data_plane
      role_data_plane:
        config_provider: yaml
    
    nginx_config:
      error_log: "/dev/stderr"
      error_log_level: "warn"
      worker_processes: "auto"
      enable_reuseport: true

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-routes
  namespace: default
data:
  apisix.yaml: |
    routes:
      # Legacy system routing: /trading/* -> trading service
      - id: trading-service-route
        uri: /trading/*
        upstream:
          type: roundrobin
          nodes:
            "trading-service.default.svc.cluster.local:15006": 1  # Route to Envoy inbound port
        plugins:
          access-control:
            policy_matrix:
              trading-service:
                - pattern: "/trading/.*"
                  allowed: true
                  description: "Full access to trading endpoints"
                - pattern: "/asset/prices"
                  allowed: true
                  description: "Read current asset prices for trading decisions"
                - pattern: "/asset/market%-data"
                  allowed: true
                  description: "Read market data for trading analysis"
                - pattern: "/asset/assets/[A-Z]+"
                  allowed: true
                  description: "Read specific asset information by symbol"
                - pattern: "/asset/assets$"
                  allowed: false
                  description: "Cannot list all assets"
                - pattern: "/asset/assets/.*/price"
                  allowed: false
                  method: "PUT"
                  description: "Cannot update asset prices"
              asset-service:
                - pattern: "/asset/.*"
                  allowed: true
                  description: "Full access to asset endpoints"
                - pattern: "/trading/trades$"
                  allowed: true
                  method: "GET"
                  description: "Read trades for position calculations"
                - pattern: "/trading/trades/symbol/.*"
                  allowed: true
                  description: "Read trades by symbol for asset analysis"
                - pattern: "/trading/stats"
                  allowed: true
                  description: "Read trading statistics for market analysis"
                - pattern: "/trading/trades$"
                  allowed: false
                  method: "POST"
                  description: "Cannot create new trades"
              default:
                - pattern: "/.*"
                  allowed: false
                  description: "Deny all access by default"
            jwt_extraction_method: "simplified"
            default_deny: true
            log_level: "info"

      # Legacy system routing: /asset/* -> asset service
      - id: asset-service-route
        uri: /asset/*
        upstream:
          type: roundrobin
          nodes:
            "asset-service.default.svc.cluster.local:15006": 1  # Route to Envoy inbound port
        plugins:
          access-control:
            policy_matrix:
              trading-service:
                - pattern: "/trading/.*"
                  allowed: true
                  description: "Full access to trading endpoints"
                - pattern: "/asset/prices"
                  allowed: true
                  description: "Read current asset prices for trading decisions"
                - pattern: "/asset/market%-data"
                  allowed: true
                  description: "Read market data for trading analysis"
                - pattern: "/asset/assets/[A-Z]+"
                  allowed: true
                  description: "Read specific asset information by symbol"
                - pattern: "/asset/assets$"
                  allowed: false
                  description: "Cannot list all assets"
                - pattern: "/asset/assets/.*/price"
                  allowed: false
                  method: "PUT"
                  description: "Cannot update asset prices"
              asset-service:
                - pattern: "/asset/.*"
                  allowed: true
                  description: "Full access to asset endpoints"
                - pattern: "/trading/trades$"
                  allowed: true
                  method: "GET"
                  description: "Read trades for position calculations"
                - pattern: "/trading/trades/symbol/.*"
                  allowed: true
                  description: "Read trades by symbol for asset analysis"
                - pattern: "/trading/stats"
                  allowed: true
                  description: "Read trading statistics for market analysis"
                - pattern: "/trading/trades$"
                  allowed: false
                  method: "POST"
                  description: "Cannot create new trades"
              default:
                - pattern: "/.*"
                  allowed: false
                  description: "Deny all access by default"
            jwt_extraction_method: "simplified"
            default_deny: true
            log_level: "info"

    
    upstreams: []
    
    #END
