apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-config
  namespace: default
data:
  config.yaml: |
    apisix:
      node_listen: 9080
      enable_ipv6: false
      enable_control: true
      control:
        ip: "0.0.0.0"
        port: 9092
    
    deployment:
      role: data_plane
      role_data_plane:
        config_provider: yaml
    
    nginx_config:
      error_log: "/dev/stderr"
      error_log_level: "warn"
      worker_processes: "auto"
      enable_reuseport: true

---
apiVersion: v1
kind: ConfigMap
metadata:
  name: apisix-routes
  namespace: default
data:
  apisix.yaml: |
    routes:
      # Legacy system routing: /trading/* -> trading service
      - id: trading-service-route
        uri: /trading/*
        upstream:
          type: roundrobin
          nodes:
            "trading-service.default.svc.cluster.local:15006": 1  # Route to Envoy inbound port
        plugins:
          proxy-rewrite:
            regex_uri: ["^/trading/(.*)", "/$1"]
          serverless-pre-function:
            phase: rewrite
            functions:
              - |
                return function(conf, ctx)
                  -- Access control validation
                  local auth_header = ngx.var.http_authorization
                  local request_uri = ngx.var.uri

                  -- Extract service name from JWT (simplified)
                  local source_service = "unknown-service"
                  if auth_header then
                    -- In production, properly decode JWT to extract service account name
                    -- For now, we'll use a simplified approach
                    source_service = "trading-service"  -- This would be extracted from JWT
                  end

                  -- Enhanced access control policies based on policy matrix
                  local access_policies = {
                    ["trading-service"] = {
                      -- Full access to trading endpoints
                      {pattern = "/trading/.*", allowed = true},
                      -- Specific asset endpoints for trading operations
                      {pattern = "/asset/prices", allowed = true},
                      {pattern = "/asset/market%-data", allowed = true},
                      {pattern = "/asset/assets/[A-Z]+", allowed = true},
                      -- Denied asset management endpoints
                      {pattern = "/asset/assets$", allowed = false},
                      {pattern = "/asset/assets/.*/price", allowed = false, method = "PUT"},
                    },
                    ["asset-service"] = {
                      -- Full access to asset endpoints
                      {pattern = "/asset/.*", allowed = true},
                      -- Specific trading endpoints for analysis
                      {pattern = "/trading/trades$", allowed = true, method = "GET"},
                      {pattern = "/trading/trades/symbol/.*", allowed = true},
                      {pattern = "/trading/stats", allowed = true},
                      -- Denied trade creation
                      {pattern = "/trading/trades$", allowed = false, method = "POST"},
                    },
                    ["default"] = {
                      {pattern = "/.*", allowed = false},
                    }
                  }

                  local service_policies = access_policies[source_service] or access_policies["default"]
                  local access_allowed = false
                  local request_method = ngx.var.request_method
                  local deny_reason = "No matching policy"

                  -- Check each policy rule in order
                  for _, policy in ipairs(service_policies) do
                    local pattern_match = string.match(request_uri, policy.pattern)
                    local method_match = true

                    -- Check method if specified in policy
                    if policy.method then
                      method_match = (request_method == policy.method)
                    end

                    if pattern_match and method_match then
                      access_allowed = policy.allowed
                      if not access_allowed then
                        deny_reason = "Explicitly denied by policy: " .. policy.pattern
                      end
                      break  -- First match wins
                    end
                  end

                  if not access_allowed then
                    local error_msg = "Access denied for service " .. source_service .. " to " .. request_method .. " " .. request_uri .. " - " .. deny_reason
                    ngx.log(ngx.WARN, error_msg)
                    ngx.status = 403
                    ngx.header["Content-Type"] = "application/json"
                    ngx.say('{"error": "' .. error_msg .. '", "service": "' .. source_service .. '", "uri": "' .. request_uri .. '", "method": "' .. request_method .. '"}')
                    ngx.exit(403)
                  end

                  -- Forward JWT token from incoming request
                  if auth_header then
                    ngx.req.set_header("Authorization", auth_header)
                    ngx.log(ngx.INFO, "Forwarding JWT token to trading service")
                  end

                  -- Add Apisix's own ServiceAccount JWT for authentication
                  local apisix_jwt_file = io.open("/var/run/secrets/kubernetes.io/serviceaccount/token", "r")
                  if apisix_jwt_file then
                    local apisix_jwt = apisix_jwt_file:read("*all")
                    apisix_jwt_file:close()
                    if apisix_jwt and apisix_jwt ~= "" then
                      ngx.req.set_header("X-Apisix-JWT", "Bearer " .. apisix_jwt:gsub("%s+", ""))
                      ngx.log(ngx.INFO, "Added Apisix JWT token for service authentication")
                    end
                  end
                end

      # Legacy system routing: /asset/* -> asset service
      - id: asset-service-route
        uri: /asset/*
        upstream:
          type: roundrobin
          nodes:
            "asset-service.default.svc.cluster.local:15006": 1  # Route to Envoy inbound port
        plugins:
          proxy-rewrite:
            regex_uri: ["^/asset/(.*)", "/$1"]
          serverless-pre-function:
            phase: rewrite
            functions:
              - |
                return function(conf, ctx)
                  -- Access control validation
                  local auth_header = ngx.var.http_authorization
                  local request_uri = ngx.var.uri

                  -- Extract service name from JWT (simplified)
                  local source_service = "unknown-service"
                  if auth_header then
                    -- In production, properly decode JWT to extract service account name
                    -- For now, we'll use a simplified approach
                    source_service = "asset-service"  -- This would be extracted from JWT
                  end

                  -- Enhanced access control policies (same as trading route)
                  local access_policies = {
                    ["trading-service"] = {
                      -- Full access to trading endpoints
                      {pattern = "/trading/.*", allowed = true},
                      -- Specific asset endpoints for trading operations
                      {pattern = "/asset/prices", allowed = true},
                      {pattern = "/asset/market%-data", allowed = true},
                      {pattern = "/asset/assets/[A-Z]+", allowed = true},
                      -- Denied asset management endpoints
                      {pattern = "/asset/assets$", allowed = false},
                      {pattern = "/asset/assets/.*/price", allowed = false, method = "PUT"},
                    },
                    ["asset-service"] = {
                      -- Full access to asset endpoints
                      {pattern = "/asset/.*", allowed = true},
                      -- Specific trading endpoints for analysis
                      {pattern = "/trading/trades$", allowed = true, method = "GET"},
                      {pattern = "/trading/trades/symbol/.*", allowed = true},
                      {pattern = "/trading/stats", allowed = true},
                      -- Denied trade creation
                      {pattern = "/trading/trades$", allowed = false, method = "POST"},
                    },
                    ["default"] = {
                      {pattern = "/.*", allowed = false},
                    }
                  }

                  local service_policies = access_policies[source_service] or access_policies["default"]
                  local access_allowed = false
                  local request_method = ngx.var.request_method
                  local deny_reason = "No matching policy"

                  -- Check each policy rule in order
                  for _, policy in ipairs(service_policies) do
                    local pattern_match = string.match(request_uri, policy.pattern)
                    local method_match = true

                    -- Check method if specified in policy
                    if policy.method then
                      method_match = (request_method == policy.method)
                    end

                    if pattern_match and method_match then
                      access_allowed = policy.allowed
                      if not access_allowed then
                        deny_reason = "Explicitly denied by policy: " .. policy.pattern
                      end
                      break  -- First match wins
                    end
                  end

                  if not access_allowed then
                    local error_msg = "Access denied for service " .. source_service .. " to " .. request_method .. " " .. request_uri .. " - " .. deny_reason
                    ngx.log(ngx.WARN, error_msg)
                    ngx.status = 403
                    ngx.header["Content-Type"] = "application/json"
                    ngx.say('{"error": "' .. error_msg .. '", "service": "' .. source_service .. '", "uri": "' .. request_uri .. '", "method": "' .. request_method .. '"}')
                    ngx.exit(403)
                  end

                  -- Forward JWT token from incoming request
                  if auth_header then
                    ngx.req.set_header("Authorization", auth_header)
                    ngx.log(ngx.INFO, "Forwarding JWT token to asset service")
                  end

                  -- Add Apisix's own ServiceAccount JWT for authentication
                  local apisix_jwt_file = io.open("/var/run/secrets/kubernetes.io/serviceaccount/token", "r")
                  if apisix_jwt_file then
                    local apisix_jwt = apisix_jwt_file:read("*all")
                    apisix_jwt_file:close()
                    if apisix_jwt and apisix_jwt ~= "" then
                      ngx.req.set_header("X-Apisix-JWT", "Bearer " .. apisix_jwt:gsub("%s+", ""))
                      ngx.log(ngx.INFO, "Added Apisix JWT token for service authentication")
                    end
                  end
                end

    
    upstreams: []
    
    #END
