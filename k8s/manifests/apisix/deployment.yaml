apiVersion: apps/v1
kind: Deployment
metadata:
  name: apisix-gateway
  namespace: default
  labels:
    app: apisix-gateway
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: apisix-gateway
      version: v1
  template:
    metadata:
      labels:
        app: apisix-gateway
        version: v1
      annotations:
        wfSidecarInjection: "false"
    spec:
      containers:
      - name: apisix
        image: apisix-custom:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 80
          name: http
        - containerPort: 9443
          name: https
        env:
        - name: APISIX_STAND_ALONE
          value: "true"
        volumeMounts:
        - name: apisix-config
          mountPath: /usr/local/apisix/conf/config.yaml
          subPath: config.yaml
        - name: apisix-routes
          mountPath: /usr/local/apisix/conf/apisix.yaml
          subPath: apisix.yaml
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
      volumes:
      - name: apisix-config
        configMap:
          name: apisix-config
      - name: apisix-routes
        configMap:
          name: apisix-routes
