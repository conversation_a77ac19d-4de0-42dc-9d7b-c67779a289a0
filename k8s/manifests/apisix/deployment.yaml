apiVersion: apps/v1
kind: Deployment
metadata:
  name: apisix-gateway
  namespace: default
  labels:
    app: apisix-gateway
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: apisix-gateway
      version: v1
  template:
    metadata:
      labels:
        app: apisix-gateway
        version: v1
      annotations:
        sidecar.envoy.io/inject: "false"
    spec:
      containers:
      - name: apisix
        image: apache/apisix:3.13.0-debian
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 9080
          name: http
        - containerPort: 9443
          name: https
        - containerPort: 9180
          name: admin
        env:
        - name: APISIX_STAND_ALONE
          value: "true"
        volumeMounts:
        - name: apisix-config
          mountPath: /usr/local/apisix/conf/config.yaml
          subPath: config.yaml
        - name: apisix-routes
          mountPath: /usr/local/apisix/conf/apisix.yaml
          subPath: apisix.yaml
        resources:
          requests:
            memory: "256Mi"
            cpu: "200m"
          limits:
            memory: "512Mi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /apisix/admin/routes
            port: 9180
          initialDelaySeconds: 30
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          httpGet:
            path: /apisix/admin/routes
            port: 9180
          initialDelaySeconds: 5
          periodSeconds: 5
          timeoutSeconds: 3
          failureThreshold: 3
        securityContext:
          runAsNonRoot: false
          allowPrivilegeEscalation: false
      volumes:
      - name: apisix-config
        configMap:
          name: apisix-config
      - name: apisix-routes
        configMap:
          name: apisix-routes
