apiVersion: apps/v1
kind: Deployment
metadata:
  name: asset-service
  namespace: default
  labels:
    app: asset-service
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: asset-service
      version: v1
  template:
    metadata:
      labels:
        app: asset-service
        version: v1
      annotations:
        sidecar.envoy.io/inject: "true"
    spec:
      containers:
      - name: asset-service
        image: asset-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: PORT
          value: "8080"
        - name: DEBUG
          value: "false"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
