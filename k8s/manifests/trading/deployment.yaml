apiVersion: apps/v1
kind: Deployment
metadata:
  name: trading-service
  namespace: default
  labels:
    app: trading-service
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: trading-service
      version: v1
  template:
    metadata:
      labels:
        app: trading-service
        version: v1
      annotations:
        wfSidecarInjection: "true"
    spec:
      containers:
      - name: trading-service
        image: trading-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8010
          name: http
        env:
        - name: PORT
          value: "8010"
        - name: APISIX_GATEWAY_URL
          value: "http://apisix-gateway.default.svc.cluster.local"
        - name: DEBUG
          value: "false"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        securityContext:
          runAsNonRoot: false
          runAsUser: 0
          allowPrivilegeEscalation: true
          readOnlyRootFilesystem: false
          capabilities:
            add:
            - NET_ADMIN
