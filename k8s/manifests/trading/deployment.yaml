apiVersion: apps/v1
kind: Deployment
metadata:
  name: trading-service
  namespace: default
  labels:
    app: trading-service
    version: v1
spec:
  replicas: 1
  selector:
    matchLabels:
      app: trading-service
      version: v1
  template:
    metadata:
      labels:
        app: trading-service
        version: v1
      annotations:
        sidecar.envoy.io/inject: "true"
    spec:
      containers:
      - name: trading-service
        image: trading-service:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8080
          name: http
        env:
        - name: PORT
          value: "8080"
        - name: APISIX_GATEWAY_URL
          value: "http://apisix-gateway.default.svc.cluster.local:9080"
        - name: DEBUG
          value: "false"
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          capabilities:
            drop:
            - ALL
