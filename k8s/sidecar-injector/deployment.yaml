apiVersion: apps/v1
kind: Deployment
metadata:
  name: sidecar-injector
  namespace: default
  labels:
    app: sidecar-injector
spec:
  replicas: 1
  selector:
    matchLabels:
      app: sidecar-injector
  template:
    metadata:
      labels:
        app: sidecar-injector
    spec:
      serviceAccountName: sidecar-injector
      containers:
      - name: webhook
        image: sidecar-injector:latest
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 8443
          name: https
        env:
        - name: TLS_CERT_FILE
          value: "/etc/certs/tls.crt"
        - name: TLS_PRIVATE_KEY_FILE
          value: "/etc/certs/tls.key"
        volumeMounts:
        - name: webhook-certs
          mountPath: /etc/certs
          readOnly: true
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          httpGet:
            path: /health
            port: 8443
            scheme: HTTPS
          initialDelaySeconds: 30
          periodSeconds: 10
        readinessProbe:
          httpGet:
            path: /health
            port: 8443
            scheme: HTTPS
          initialDelaySeconds: 5
          periodSeconds: 5
        securityContext:
          runAsNonRoot: true
          runAsUser: 1000
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          capabilities:
            drop:
            - ALL
      volumes:
      - name: webhook-certs
        secret:
          secretName: webhook-server-certs
