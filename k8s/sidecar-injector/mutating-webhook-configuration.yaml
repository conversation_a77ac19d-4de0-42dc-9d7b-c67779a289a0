apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: sidecar-injector-webhook
webhooks:
- name: sidecar-injector.envoy.io
  rules:
  - operations: ["CREATE"]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
  clientConfig:
    service:
      name: sidecar-injector-webhook
      namespace: envoy-system
      path: "/mutate"
    caBundle: 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
  admissionReviewVersions: ["v1", "v1beta1"]
  sideEffects: None
  failurePolicy: Fail
  namespaceSelector:
    matchLabels:
      sidecar-injection: enabled
