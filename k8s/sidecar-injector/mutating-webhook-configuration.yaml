apiVersion: admissionregistration.k8s.io/v1
kind: MutatingWebhookConfiguration
metadata:
  name: sidecar-injector-webhook
webhooks:
- name: sidecar-injector.envoy.io
  rules:
  - operations: ["CREATE"]
    apiGroups: [""]
    apiVersions: ["v1"]
    resources: ["pods"]
  clientConfig:
    service:
      name: sidecar-injector-webhook
      namespace: envoy-system
      path: "/mutate"
    caBundle: 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
  admissionReviewVersions: ["v1", "v1beta1"]
  sideEffects: None
  failurePolicy: Fail
  namespaceSelector:
    matchLabels:
      sidecar-injection: enabled
