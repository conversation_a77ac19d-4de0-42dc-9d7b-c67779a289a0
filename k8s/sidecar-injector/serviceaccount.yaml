apiVersion: v1
kind: ServiceAccount
metadata:
  name: sidecar-injector-webhook
  namespace: envoy-system
  labels:
    app: sidecar-injector-webhook
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: sidecar-injector-webhook
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["configmaps"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["secrets"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: sidecar-injector-webhook
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: sidecar-injector-webhook
subjects:
- kind: ServiceAccount
  name: sidecar-injector-webhook
  namespace: envoy-system
