#!/bin/bash

# Build all Docker images for the k8s-envoy project
set -e

echo "🐳 Building Docker images..."

# Build sidecar injector webhook
echo "📦 Building sidecar injector webhook..."
cd sidecar-injector/webhook
docker build -t sidecar-injector:latest .
cd ../..

# Build custom Envoy image
echo "📦 Building custom Envoy image..."
cd envoy
docker build -t custom-envoy:latest .
cd ..

# Build trading service
echo "📦 Building trading service..."
cd services/trading
docker build -t trading-service:latest .
cd ../..

# Build asset service
echo "📦 Building asset service..."
cd services/asset
docker build -t asset-service:latest .
cd ../..

echo "✅ All images built successfully!"

echo "📋 Built images:"
docker images | grep -E "(sidecar-injector|custom-envoy|trading-service|asset-service)"

echo "📋 Next steps:"
echo "   1. Load images into minikube: minikube image load <image-name>"
echo "   2. Or push to registry: docker tag <image> <registry>/<image> && docker push <registry>/<image>"
