#!/bin/bash

# Deploy Legacy System Architecture
# Deploys all components for the legacy system with Apisix gateway routing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

NAMESPACE="default"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo -e "${BLUE}🚀 Deploying System Architecture${NC}"
echo "=============================================="

# Function to wait for deployment
wait_for_deployment() {
    local deployment=$1
    local namespace=$2
    echo -e "${YELLOW}Waiting for $deployment to be ready...${NC}"
    kubectl wait --for=condition=available deployment/$deployment -n $namespace --timeout=300s
}

# Function to wait for pods
wait_for_pods() {
    local label=$1
    local namespace=$2
    echo -e "${YELLOW}Waiting for pods with label $label to be ready...${NC}"
    kubectl wait --for=condition=ready pod -l $label -n $namespace --timeout=300s
}

# Deploy webhook certificates
deploy_webhook_certs() {
    echo -e "\n${BLUE}📜 Deploying Webhook Certificates${NC}"
    echo "=================================="
    
    cd "$PROJECT_ROOT"
    
    if [ ! -f "certs/webhook-server-tls.crt" ]; then
        echo "Generating webhook certificates..."
        ./scripts/setup/generate-certs.sh
    fi
    
    # Create certificate secret
    kubectl create secret tls webhook-server-certs \
        --cert=certs/webhook-server-tls.crt \
        --key=certs/webhook-server-tls.key \
        -n $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -
    
    echo -e "${GREEN}✅ Webhook certificates deployed${NC}"
}

# Deploy sidecar injector
deploy_sidecar_injector() {
    echo -e "\n${BLUE}🔧 Deploying Sidecar Injector${NC}"
    echo "==============================="
    
    # Build and load sidecar injector image
    echo "Building sidecar injector image..."
    cd "$PROJECT_ROOT/sidecar-injector"
    docker build -t sidecar-injector:latest .
    
    # Load image into kind cluster if using kind
    if command -v kind &> /dev/null && kind get clusters | grep -q "kind"; then
        echo "Loading image into kind cluster..."
        kind load docker-image sidecar-injector:latest
    fi
    
    cd "$PROJECT_ROOT"
    
    # Deploy sidecar injector components
    kubectl apply -f k8s/manifests/sidecar-injector/
    
    wait_for_deployment "sidecar-injector" $NAMESPACE
    echo -e "${GREEN}✅ Sidecar injector deployed${NC}"
}

# Deploy Envoy configuration
deploy_envoy_config() {
    echo -e "\n${BLUE}⚙️  Deploying Envoy Configuration${NC}"
    echo "=================================="
    
    kubectl apply -f k8s/envoy/configmap.yaml
    echo -e "${GREEN}✅ Envoy configuration deployed${NC}"
}

# Deploy Apisix gateway
deploy_apisix() {
    echo -e "\n${BLUE}🌐 Deploying Apisix Gateway${NC}"
    echo "============================"
    
    # Deploy Apisix components
    kubectl apply -f k8s/manifests/apisix/
    
    wait_for_deployment "apisix-gateway" $NAMESPACE
    echo -e "${GREEN}✅ Apisix gateway deployed${NC}"
}

# Build and deploy services
deploy_services() {
    echo -e "\n${BLUE}🏗️  Building and Deploying Services${NC}"
    echo "====================================="
    
    cd "$PROJECT_ROOT"
    
    # Build trading service
    echo "Building trading service..."
    cd services/trading
    docker build -t trading-service:latest .
    
    # Build asset service
    echo "Building asset service..."
    cd ../asset
    docker build -t asset-service:latest .
    
    cd "$PROJECT_ROOT"
    
    # Load images into kind cluster if using kind
    if command -v kind &> /dev/null && kind get clusters | grep -q "kind"; then
        echo "Loading service images into kind cluster..."
        kind load docker-image trading-service:latest
        kind load docker-image asset-service:latest
    fi
    
    # Deploy services
    kubectl apply -f k8s/manifests/trading/
    kubectl apply -f k8s/manifests/asset/
    
    wait_for_deployment "trading-service" $NAMESPACE
    wait_for_deployment "asset-service" $NAMESPACE
    
    echo -e "${GREEN}✅ Services deployed${NC}"
}

# Verify deployment
verify_deployment() {
    echo -e "\n${BLUE}🔍 Verifying Deployment${NC}"
    echo "========================"
    
    echo -e "\n${YELLOW}Pod Status:${NC}"
    kubectl get pods -n $NAMESPACE
    
    echo -e "\n${YELLOW}Service Status:${NC}"
    kubectl get services -n $NAMESPACE
    
    echo -e "\n${YELLOW}ConfigMaps:${NC}"
    kubectl get configmaps -n $NAMESPACE | grep -E "(envoy|apisix|access-control)"
    
    echo -e "\n${YELLOW}Webhook Configuration:${NC}"
    kubectl get mutatingwebhookconfiguration sidecar-injector
    
    # Check sidecar injection
    echo -e "\n${YELLOW}Checking Sidecar Injection:${NC}"
    
    # Trading service should have Envoy sidecar
    trading_containers=$(kubectl get pod -n $NAMESPACE -l app=trading-service -o jsonpath='{.items[0].spec.containers[*].name}')
    if echo "$trading_containers" | grep -q envoy; then
        echo -e "${GREEN}✅ Trading service has Envoy sidecar${NC}"
    else
        echo -e "${RED}❌ Trading service missing Envoy sidecar${NC}"
    fi
    
    # Asset service should have Envoy sidecar
    asset_containers=$(kubectl get pod -n $NAMESPACE -l app=asset-service -o jsonpath='{.items[0].spec.containers[*].name}')
    if echo "$asset_containers" | grep -q envoy; then
        echo -e "${GREEN}✅ Asset service has Envoy sidecar${NC}"
    else
        echo -e "${RED}❌ Asset service missing Envoy sidecar${NC}"
    fi
    
    # Apisix should NOT have Envoy sidecar
    apisix_containers=$(kubectl get pod -n $NAMESPACE -l app=apisix-gateway -o jsonpath='{.items[0].spec.containers[*].name}')
    if echo "$apisix_containers" | grep -q envoy; then
        echo -e "${RED}❌ Apisix has Envoy sidecar (should be excluded)${NC}"
    else
        echo -e "${GREEN}✅ Apisix correctly excluded from sidecar injection${NC}"
    fi
}

# Main deployment function
main() {
    echo -e "${BLUE}Starting Legacy System Deployment...${NC}\n"
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        echo -e "${RED}❌ kubectl is not installed or not in PATH${NC}"
        exit 1
    fi
    
    # Check if Docker is available
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker is not installed or not in PATH${NC}"
        exit 1
    fi
    
    # Deploy components in order
    deploy_webhook_certs
    deploy_envoy_config
    deploy_sidecar_injector
    deploy_apisix
    deploy_services
    
    # Wait a bit for everything to settle
    echo -e "\n${YELLOW}Waiting for system to stabilize...${NC}"
    sleep 30
    
    # Verify deployment
    verify_deployment
    
    echo -e "\n${GREEN}🎉 Legacy System Deployment Complete!${NC}"
    echo "=============================================="
    echo -e "${YELLOW}Next steps:${NC}"
    echo "1. Run tests: ./scripts/test/test-system.sh"
    echo "2. Check logs: kubectl logs -f deployment/apisix-gateway"
    echo "3. Monitor traffic: kubectl logs -f deployment/trading-service -c envoy"
}

# Run main function
main "$@"
