#!/bin/bash

# Deploy Legacy System Architecture
# Deploys all components for the legacy system with Apisix gateway routing

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

NAMESPACE="default"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"

echo -e "${BLUE}🚀 Deploying System Architecture${NC}"
echo "=============================================="

# Function to wait for deployment
wait_for_deployment() {
    local deployment=$1
    local namespace=$2
    echo -e "${YELLOW}Waiting for $deployment to be ready...${NC}"
    kubectl wait --for=condition=available deployment/$deployment -n $namespace --timeout=300s
}


# Generate and deploy webhook certificates
deploy_webhook_certs() {
    echo -e "\n${BLUE}📜 Generating and Deploying Webhook Certificates${NC}"
    echo "=================================================="

    cd "$PROJECT_ROOT"

    # Create certificate directory for webhook
    WEBHOOK_CERT_DIR="sidecar-injector/certs"
    mkdir -p $WEBHOOK_CERT_DIR

    # Check if certificates already exist
    if [ ! -f "$WEBHOOK_CERT_DIR/tls.crt" ] || [ ! -f "$WEBHOOK_CERT_DIR/tls.key" ]; then
        echo "🔐 Generating certificates for webhook server..."

        # Generate CA private key for webhook certificates
        echo "📝 Generating CA private key..."
        openssl genrsa -out $WEBHOOK_CERT_DIR/ca.key 4096

        # Generate CA certificate
        echo "📝 Generating CA certificate..."
        openssl req -new -x509 -key $WEBHOOK_CERT_DIR/ca.key -sha256 \
            -subj "/C=US/ST=CA/O=K8sEnvoy/CN=k8s-envoy-webhook-ca" \
            -days 3650 -out $WEBHOOK_CERT_DIR/ca.crt

        # Generate webhook private key
        echo "📝 Generating webhook private key..."
        openssl genrsa -out $WEBHOOK_CERT_DIR/tls.key 4096

        # Create webhook certificate signing request
        echo "📝 Creating webhook certificate signing request..."
        cat > $WEBHOOK_CERT_DIR/webhook.conf <<EOF
[req]
default_bits = 4096
prompt = no
distinguished_name = req_distinguished_name
req_extensions = req_ext

[req_distinguished_name]
C = US
ST = CA
O = K8sEnvoy
CN = sidecar-injector-webhook

[req_ext]
subjectAltName = @alt_names

[alt_names]
DNS.1 = sidecar-injector-webhook
DNS.2 = sidecar-injector-webhook.default.svc
DNS.3 = sidecar-injector-webhook.default.svc.cluster.local
EOF

        openssl req -new -key $WEBHOOK_CERT_DIR/tls.key \
            -out $WEBHOOK_CERT_DIR/webhook.csr \
            -config $WEBHOOK_CERT_DIR/webhook.conf

        # Generate webhook certificate signed by CA
        echo "📝 Generating webhook certificate..."
        openssl x509 -req -in $WEBHOOK_CERT_DIR/webhook.csr \
            -CA $WEBHOOK_CERT_DIR/ca.crt -CAkey $WEBHOOK_CERT_DIR/ca.key \
            -CAcreateserial -out $WEBHOOK_CERT_DIR/tls.crt \
            -days 365 -extensions req_ext -extfile $WEBHOOK_CERT_DIR/webhook.conf

        # Clean up CSR files
        rm $WEBHOOK_CERT_DIR/webhook.csr $WEBHOOK_CERT_DIR/webhook.conf

        echo "✅ Webhook certificates generated successfully!"
        echo "📁 Webhook certificates: $WEBHOOK_CERT_DIR/"

        # Display certificate information
        echo "🔍 Certificate information:"
        echo "CA Certificate:"
        openssl x509 -in $WEBHOOK_CERT_DIR/ca.crt -text -noout | grep -A 2 "Subject:" || true

        echo "Webhook Certificate:"
        openssl x509 -in $WEBHOOK_CERT_DIR/tls.crt -text -noout | grep -A 2 "Subject:" || true
    else
        echo "✅ Webhook certificates already exist, skipping generation"
    fi

    # Create certificate secret using the correct paths
    kubectl create secret tls webhook-server-certs \
        --cert=$WEBHOOK_CERT_DIR/tls.crt \
        --key=$WEBHOOK_CERT_DIR/tls.key \
        -n $NAMESPACE --dry-run=client -o yaml | kubectl apply -f -

    # Update webhook configuration with CA bundle
    echo "📝 Updating webhook configuration with CA bundle..."
    CA_BUNDLE=$(base64 -w 0 < $WEBHOOK_CERT_DIR/ca.crt)

    # Create a temporary webhook config with the correct CA bundle
    sed "s/caBundle:.*/caBundle: $CA_BUNDLE/" "$PROJECT_ROOT/k8s/sidecar-injector/mutating-webhook-configuration.yaml" > /tmp/webhook-config.yaml

    echo -e "${GREEN}✅ Webhook certificates deployed${NC}"
}

# Deploy sidecar injector
deploy_sidecar_injector() {
    echo -e "\n${BLUE}🔧 Deploying Sidecar Injector${NC}"
    echo "==============================="

    # Build and load sidecar injector image
    echo "Building sidecar injector image..."
    cd "$PROJECT_ROOT/sidecar-injector/webhook"
    docker build -t sidecar-injector:latest .
    
    cd "$PROJECT_ROOT"
    
    # Deploy sidecar injector components (excluding webhook config first)
    kubectl apply -f k8s/sidecar-injector/serviceaccount.yaml
    kubectl apply -f k8s/sidecar-injector/service.yaml
    kubectl apply -f k8s/sidecar-injector/deployment.yaml

    # Deploy webhook configuration with updated CA bundle
    if [ -f "/tmp/webhook-config.yaml" ]; then
        kubectl apply -f /tmp/webhook-config.yaml
        rm -f /tmp/webhook-config.yaml
    else
        kubectl apply -f k8s/sidecar-injector/mutating-webhook-configuration.yaml
    fi

    wait_for_deployment "sidecar-injector" $NAMESPACE
    echo -e "${GREEN}✅ Sidecar injector deployed${NC}"
}

# Deploy Envoy configuration
deploy_envoy_config() {
    echo -e "\n${BLUE}⚙️  Deploying Envoy Configuration${NC}"
    echo "=================================="
    
    kubectl apply -f k8s/envoy/configmap.yaml
    echo -e "${GREEN}✅ Envoy configuration deployed${NC}"
}

# Deploy Apisix gateway
deploy_apisix() {
    echo -e "\n${BLUE}🌐 Deploying Apisix Gateway${NC}"
    echo "============================"
    
    # Deploy Apisix components
    kubectl apply -f k8s/manifests/apisix/
    
    wait_for_deployment "apisix-gateway" $NAMESPACE
    echo -e "${GREEN}✅ Apisix gateway deployed${NC}"
}

# Build and deploy services
deploy_services() {
    echo -e "\n${BLUE}🏗️  Building and Deploying Services${NC}"
    echo "====================================="
    
    cd "$PROJECT_ROOT"
    
    # Build trading service
    echo "Building trading service..."
    cd services/trading
    docker build -t trading-service:latest .
    
    # Build asset service
    echo "Building asset service..."
    cd ../asset
    docker build -t asset-service:latest .
    
    cd "$PROJECT_ROOT"

    # Deploy services
    kubectl apply -f k8s/manifests/trading/
    kubectl apply -f k8s/manifests/asset/
    
    wait_for_deployment "trading-service" $NAMESPACE
    wait_for_deployment "asset-service" $NAMESPACE
    
    echo -e "${GREEN}✅ Services deployed${NC}"
}

# Verify deployment
verify_deployment() {
    echo -e "\n${BLUE}🔍 Verifying Deployment${NC}"
    echo "========================"
    
    echo -e "\n${YELLOW}Pod Status:${NC}"
    kubectl get pods -n $NAMESPACE
    
    echo -e "\n${YELLOW}Service Status:${NC}"
    kubectl get services -n $NAMESPACE
    
    echo -e "\n${YELLOW}ConfigMaps:${NC}"
    kubectl get configmaps -n $NAMESPACE | grep -E "(envoy|apisix|access-control)"
    
    echo -e "\n${YELLOW}Webhook Configuration:${NC}"
    kubectl get mutatingwebhookconfiguration sidecar-injector
    
    # Check sidecar injection
    echo -e "\n${YELLOW}Checking Sidecar Injection:${NC}"
    
    # Trading service should have Envoy sidecar
    trading_containers=$(kubectl get pod -n $NAMESPACE -l app=trading-service -o jsonpath='{.items[0].spec.containers[*].name}')
    if echo "$trading_containers" | grep -q envoy; then
        echo -e "${GREEN}✅ Trading service has Envoy sidecar${NC}"
    else
        echo -e "${RED}❌ Trading service missing Envoy sidecar${NC}"
    fi
    
    # Asset service should have Envoy sidecar
    asset_containers=$(kubectl get pod -n $NAMESPACE -l app=asset-service -o jsonpath='{.items[0].spec.containers[*].name}')
    if echo "$asset_containers" | grep -q envoy; then
        echo -e "${GREEN}✅ Asset service has Envoy sidecar${NC}"
    else
        echo -e "${RED}❌ Asset service missing Envoy sidecar${NC}"
    fi
    
    # Apisix should NOT have Envoy sidecar
    apisix_containers=$(kubectl get pod -n $NAMESPACE -l app=apisix-gateway -o jsonpath='{.items[0].spec.containers[*].name}')
    if echo "$apisix_containers" | grep -q envoy; then
        echo -e "${RED}❌ Apisix has Envoy sidecar (should be excluded)${NC}"
    else
        echo -e "${GREEN}✅ Apisix correctly excluded from sidecar injection${NC}"
    fi
}

# Main deployment function
main() {
    echo -e "${BLUE}Starting System Deployment...${NC}\n"
    
    # Check if kubectl is available
    if ! command -v kubectl &> /dev/null; then
        echo -e "${RED}❌ kubectl is not installed or not in PATH${NC}"
        exit 1
    fi
    
    # Check if Docker is available
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker is not installed or not in PATH${NC}"
        exit 1
    fi
    
    # Deploy components in order
    deploy_webhook_certs
    deploy_envoy_config
    deploy_sidecar_injector
    deploy_apisix
    deploy_services
    
    # Wait a bit for everything to settle
    seconds=10
    echo -e "\n${YELLOW}Wait ${seconds} seconds for system to stabilize...${NC}"
    sleep ${seconds}
    
    # Verify deployment
    verify_deployment
    
    echo -e "\n${GREEN}🎉 System Deployment Complete!${NC}"
    echo "=============================================="
    echo -e "${YELLOW}Next steps:${NC}"
    echo "1. Run tests: ./scripts/test/test-system.sh"
    echo "2. Check logs: kubectl logs -f deployment/apisix-gateway"
    echo "3. Monitor traffic: kubectl logs -f deployment/trading-service -c envoy-sidecar"
}

# Run main function
main "$@"
