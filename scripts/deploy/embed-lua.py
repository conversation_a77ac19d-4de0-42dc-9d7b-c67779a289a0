#!/usr/bin/env python3
"""
Embed Lua filter code into Envoy configuration.
This script reads the envoy-config.yaml template and embeds the external Lua files.
"""

import sys
import yaml
import os

def read_file(filepath):
    """Read file content safely."""
    try:
        with open(filepath, 'r') as f:
            return f.read()
    except FileNotFoundError:
        print(f"Error: File {filepath} not found", file=sys.stderr)
        sys.exit(1)

def embed_lua_filters(config_path, inbound_lua_path, outbound_lua_path, output_path):
    """Embed Lua filter code into Envoy configuration."""
    
    # Read the base config
    config_content = read_file(config_path)
    
    # Read Lua files
    inbound_lua = read_file(inbound_lua_path)
    outbound_lua = read_file(outbound_lua_path)
    
    # Parse YAML
    try:
        config = yaml.safe_load(config_content)
    except yaml.YAMLError as e:
        print(f"Error parsing YAML: {e}", file=sys.stderr)
        sys.exit(1)
    
    # Find and update Lu<PERSON> filters
    listeners = config.get('static_resources', {}).get('listeners', [])
    
    for listener in listeners:
        filter_chains = listener.get('filter_chains', [])
        for filter_chain in filter_chains:
            filters = filter_chain.get('filters', [])
            for filter_config in filters:
                if filter_config.get('name') == 'envoy.filters.network.http_connection_manager':
                    typed_config = filter_config.get('typed_config', {})
                    http_filters = typed_config.get('http_filters', [])
                    
                    for http_filter in http_filters:
                        if http_filter.get('name') == 'envoy.filters.http.lua':
                            lua_config = http_filter.get('typed_config', {})
                            
                            # Determine which Lua code to embed based on listener name
                            listener_name = listener.get('name', '')
                            if 'inbound' in listener_name:
                                lua_config['inline_code'] = inbound_lua
                            elif 'outbound' in listener_name:
                                lua_config['inline_code'] = outbound_lua
    
    # Write the updated config
    try:
        with open(output_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, sort_keys=False)
        print(f"Successfully embedded Lua code into {output_path}")
    except Exception as e:
        print(f"Error writing output file: {e}", file=sys.stderr)
        sys.exit(1)

def main():
    if len(sys.argv) != 5:
        print("Usage: embed-lua.py <config.yaml> <inbound.lua> <outbound.lua> <output.yaml>")
        sys.exit(1)
    
    config_path = sys.argv[1]
    inbound_lua_path = sys.argv[2]
    outbound_lua_path = sys.argv[3]
    output_path = sys.argv[4]
    
    embed_lua_filters(config_path, inbound_lua_path, outbound_lua_path, output_path)

if __name__ == "__main__":
    main()
