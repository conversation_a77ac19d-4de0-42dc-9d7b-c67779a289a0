#!/bin/bash

# Build Docker images for services
set -e

echo "🏗️  Building Docker images..."

# Check if we're using minikube
if command -v minikube &> /dev/null && minikube status &> /dev/null; then
    echo "🔧 Using minikube Docker environment..."
    eval $(minikube docker-env)
fi

# build injector first 
echo "📦 Building sidecar-injector image..."
cd sidecar-injector/webhook
docker build -t sidecar-injector:latest .
echo "✅ sidecar-injector image built"

# Build trading service
echo "📦 Building trading-service image..."
cd ../../services/trading
docker build -t trading-service:latest .
echo "✅ trading-service image built"

# Build asset service
echo "📦 Building asset-service image..."
cd ../../services/asset
docker build -t asset-service:latest .
echo "✅ asset-service image built"

# Return to root directory
cd ../..

echo "✅ All images built successfully!"
echo "📋 Built images:"
echo "   - trading-service:latest"
echo "   - asset-service:latest"

# List images
echo "🔍 Verifying images..."
docker images | grep -E "(trading-service|asset-service)"

echo "📋 Next steps:"
echo "   1. Run ./deploy-services.sh to deploy the services"
echo "   2. Run ./test-system.sh to test the system"
