#!/bin/bash

# Cleanup script to remove all deployed resources
set -e

echo "🧹 Cleaning up k8s-envoy deployment..."

# Remove sidecar injector webhook
echo "🔒 Removing sidecar injector webhook..."
kubectl delete -f k8s/sidecar-injector/ --ignore-not-found=true

# Remove Envoy configuration
echo "⚙️  Removing Envoy configuration..."
kubectl delete -f k8s/envoy/ --ignore-not-found=true

# Remove deployments
echo "📦 Removing deployments..."
kubectl delete -f k8s/manifests/apisix/deployment.yaml --ignore-not-found=true
kubectl delete -f k8s/manifests/asset/deployment.yaml --ignore-not-found=true
kubectl delete -f k8s/manifests/trading/deployment.yaml --ignore-not-found=true

# Remove services
echo "🌐 Removing services..."
kubectl delete -f k8s/manifests/apisix/service.yaml --ignore-not-found=true
kubectl delete -f k8s/manifests/asset/service.yaml --ignore-not-found=true
kubectl delete -f k8s/manifests/trading/service.yaml --ignore-not-found=true

# Remove configmaps
echo "⚙️  Removing configmaps..."
kubectl delete -f k8s/manifests/apisix/configmap.yaml --ignore-not-found=true
kubectl delete configmap apisix-access-control --ignore-not-found=true
kubectl delete configmap envoy-config --ignore-not-found=true

# Remove webhook secrets
echo "🔐 Removing webhook secrets..."
kubectl delete secret webhook-certs -n envoy-system --ignore-not-found=true

# Wait for cleanup
echo "⏳ Waiting for resources to be cleaned up..."
sleep 10

# Verify cleanup
echo "🔍 Verifying cleanup..."
echo "Remaining pods:"
kubectl get pods | grep -E "(trading|asset|apisix)" || echo "No remaining pods"

echo "Remaining services:"
kubectl get services | grep -E "(trading|asset|apisix)" || echo "No remaining services"

echo "Webhook status:"
kubectl get mutatingwebhookconfigurations | grep sidecar-injector || echo "No webhook configurations"

echo "Envoy-system namespace:"
kubectl get all -n envoy-system || echo "No resources in envoy-system namespace"

echo "✅ Cleanup completed!"
echo "📋 To completely reset:"
echo "   - Remove Docker images: docker rmi trading-service:latest asset-service:latest sidecar-injector:latest"
echo "   - Remove webhook certificates: rm -rf sidecar-injector/certs"
echo "   - Delete minikube: minikube delete"
