#!/bin/bash

# Cleanup script to remove all deployed resources
set -e

echo "🧹 Cleaning up k8s-envoy deployment..."

# Remove Istio configurations
echo "🔒 Removing Istio configurations..."
kubectl delete -f k8s/istio/gateways/virtual-services.yaml --ignore-not-found=true
kubectl delete -f k8s/istio/gateways/destination-rules.yaml --ignore-not-found=true
kubectl delete -f k8s/istio/policies/authorization-policy.yaml --ignore-not-found=true
kubectl delete -f k8s/istio/policies/peer-authentication.yaml --ignore-not-found=true

# Remove deployments
echo "📦 Removing deployments..."
kubectl delete -f k8s/manifests/apisix/deployment.yaml --ignore-not-found=true
kubectl delete -f k8s/manifests/asset/deployment.yaml --ignore-not-found=true
kubectl delete -f k8s/manifests/trading/deployment.yaml --ignore-not-found=true

# Remove services
echo "🌐 Removing services..."
kubectl delete -f k8s/manifests/apisix/service.yaml --ignore-not-found=true
kubectl delete -f k8s/manifests/asset/service.yaml --ignore-not-found=true
kubectl delete -f k8s/manifests/trading/service.yaml --ignore-not-found=true

# Remove configmaps
echo "⚙️  Removing configmaps..."
kubectl delete -f k8s/manifests/apisix/configmap.yaml --ignore-not-found=true

# Remove service accounts
echo "👤 Removing service accounts..."
kubectl delete -f k8s/manifests/apisix/serviceaccount.yaml --ignore-not-found=true
kubectl delete -f k8s/manifests/asset/serviceaccount.yaml --ignore-not-found=true
kubectl delete -f k8s/manifests/trading/serviceaccount.yaml --ignore-not-found=true

# Wait for cleanup
echo "⏳ Waiting for resources to be cleaned up..."
sleep 10

# Verify cleanup
echo "🔍 Verifying cleanup..."
echo "Remaining pods:"
kubectl get pods | grep -E "(trading|asset|apisix)" || echo "No remaining pods"

echo "Remaining services:"
kubectl get services | grep -E "(trading|asset|apisix)" || echo "No remaining services"

echo "✅ Cleanup completed!"
echo "📋 To completely reset:"
echo "   - Remove Docker images: docker rmi trading-service:latest asset-service:latest"
echo "   - Uninstall Istio: istioctl uninstall --purge"
echo "   - Delete minikube: minikube delete"
