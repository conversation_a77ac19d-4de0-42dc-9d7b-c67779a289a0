#!/bin/bash

# Create Kubernetes secrets from generated certificates
set -e

echo "🔐 Creating Kubernetes secrets from certificates..."

CERT_DIR="envoy/certs"
WEBHOOK_CERT_DIR="sidecar-injector/certs"

# Check if certificates exist
if [ ! -f "$CERT_DIR/ca.crt" ] || [ ! -f "$CERT_DIR/tls.crt" ] || [ ! -f "$CERT_DIR/tls.key" ]; then
    echo "❌ Envoy certificates not found. Please run ./generate-certs.sh first."
    exit 1
fi

if [ ! -f "$WEBHOOK_CERT_DIR/tls.crt" ] || [ ! -f "$WEBHOOK_CERT_DIR/tls.key" ]; then
    echo "❌ Webhook certificates not found. Please run ./generate-certs.sh first."
    exit 1
fi

# Create namespace if it doesn't exist
kubectl create namespace envoy-system --dry-run=client -o yaml | kubectl apply -f -

# Create Envoy certificates secret
echo "📝 Creating Envoy certificates secret..."
kubectl create secret tls envoy-certs \
    --cert=$CERT_DIR/tls.crt \
    --key=$CERT_DIR/tls.key \
    --namespace=default \
    --dry-run=client -o yaml | kubectl apply -f -

# Add CA certificate to the secret
kubectl create secret generic envoy-ca \
    --from-file=ca.crt=$CERT_DIR/ca.crt \
    --namespace=default \
    --dry-run=client -o yaml | kubectl apply -f -

# Create webhook certificates secret
echo "📝 Creating webhook certificates secret..."
kubectl create secret tls webhook-certs \
    --cert=$WEBHOOK_CERT_DIR/tls.crt \
    --key=$WEBHOOK_CERT_DIR/tls.key \
    --namespace=envoy-system \
    --dry-run=client -o yaml | kubectl apply -f -

# Get CA bundle for webhook configuration
CA_BUNDLE=$(cat $CERT_DIR/ca.crt | base64 | tr -d '\n')

# Create webhook configuration template
cat > k8s/sidecar-injector/mutating-webhook-configuration.yaml <<EOF
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingAdmissionWebhook
metadata:
  name: sidecar-injector-webhook
rules:
- operations: ["CREATE"]
  apiGroups: [""]
  apiVersions: ["v1"]
  resources: ["pods"]
clientConfig:
  service:
    name: sidecar-injector-webhook
    namespace: envoy-system
    path: "/mutate"
  caBundle: $CA_BUNDLE
admissionReviewVersions: ["v1", "v1beta1"]
sideEffects: None
failurePolicy: Fail
namespaceSelector:
  matchLabels:
    sidecar-injection: enabled
EOF

echo "✅ Secrets created successfully!"
echo "📋 Created secrets:"
kubectl get secrets -n default | grep -E "(envoy-certs|envoy-ca)"
kubectl get secrets -n envoy-system | grep webhook-certs

echo "📋 Next steps:"
echo "   1. Deploy the sidecar injector webhook"
echo "   2. Label namespaces for sidecar injection: kubectl label namespace default sidecar-injection=enabled"
echo "   3. Deploy services with sidecar injection annotation"
