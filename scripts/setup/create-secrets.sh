#!/bin/bash

# Create Kubernetes secrets from generated webhook certificates
set -e

echo "🔐 Creating Kubernetes secrets from webhook certificates..."

WEBHOOK_CERT_DIR="sidecar-injector/certs"

# Check if webhook certificates exist
if [ ! -f "$WEBHOOK_CERT_DIR/tls.crt" ] || [ ! -f "$WEBHOOK_CERT_DIR/tls.key" ] || [ ! -f "$WEBHOOK_CERT_DIR/ca.crt" ]; then
    echo "❌ Webhook certificates not found. Please run ./generate-certs.sh first."
    exit 1
fi

# Create namespace if it doesn't exist
kubectl create namespace envoy-system --dry-run=client -o yaml | kubectl apply -f -

# Create webhook certificates secret
echo "📝 Creating webhook certificates secret..."
kubectl create secret tls webhook-certs \
    --cert=$WEBHOOK_CERT_DIR/tls.crt \
    --key=$WEBHOOK_CERT_DIR/tls.key \
    --namespace=envoy-system \
    --dry-run=client -o yaml | kubectl apply -f -

# Get CA bundle for webhook configuration
CA_BUNDLE=$(cat $WEBHOOK_CERT_DIR/ca.crt | base64 | tr -d '\n')

# Create webhook configuration template
cat > k8s/sidecar-injector/mutating-webhook-configuration.yaml <<EOF
apiVersion: admissionregistration.k8s.io/v1
kind: MutatingAdmissionWebhook
metadata:
  name: sidecar-injector-webhook
rules:
- operations: ["CREATE"]
  apiGroups: [""]
  apiVersions: ["v1"]
  resources: ["pods"]
clientConfig:
  service:
    name: sidecar-injector-webhook
    namespace: envoy-system
    path: "/mutate"
  caBundle: $CA_BUNDLE
admissionReviewVersions: ["v1", "v1beta1"]
sideEffects: None
failurePolicy: Fail
namespaceSelector:
  matchLabels:
    sidecar-injection: enabled
EOF

echo "✅ Webhook secrets created successfully!"
echo "📋 Created secrets:"
kubectl get secrets -n envoy-system | grep webhook-certs

echo "📋 Next steps:"
echo "   1. Deploy the sidecar injector webhook"
echo "   2. Label namespaces for sidecar injection: kubectl label namespace default sidecar-injection=enabled"
echo "   3. Deploy services with sidecar injection annotation"
