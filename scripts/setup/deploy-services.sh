#!/bin/bash

# Deploy all services to Kubernetes
set -e

echo "🚀 Deploying services to Kubernetes..."

# Generate webhook certificates if they don't exist
if [ ! -f "sidecar-injector/certs/ca.crt" ]; then
    echo "🔐 Generating webhook certificates..."
    ./scripts/setup/generate-certs.sh
fi

# Create secrets from webhook certificates
echo "🔐 Creating Kubernetes secrets..."
./scripts/setup/create-secrets.sh

# Deploy sidecar injector webhook
echo "📦 Deploying sidecar injector webhook..."
kubectl apply -f k8s/sidecar-injector/

# Deploy Envoy configuration
echo "📦 Deploying Envoy configuration..."
kubectl apply -f k8s/envoy/

# Enable sidecar injection for default namespace
echo "🏷️ Enabling sidecar injection for default namespace..."
kubectl label namespace default sidecar-injection=enabled --overwrite

# Deploy services
echo "🌐 Creating services..."
kubectl apply -f k8s/manifests/trading/service.yaml
kubectl apply -f k8s/manifests/asset/service.yaml
kubectl apply -f k8s/manifests/apisix/service.yaml

# Deploy Apisix configuration
echo "⚙️  Creating Apisix configuration..."
kubectl apply -f k8s/manifests/apisix/configmap.yaml

# Deploy applications
echo "📦 Deploying applications..."
kubectl apply -f k8s/manifests/trading/deployment.yaml
kubectl apply -f k8s/manifests/asset/deployment.yaml
kubectl apply -f k8s/manifests/apisix/deployment.yaml

# Wait for sidecar injector to be ready
echo "⏳ Waiting for sidecar injector to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/sidecar-injector-webhook -n envoy-system

# Wait for deployments to be ready
echo "⏳ Waiting for deployments to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/trading-service
kubectl wait --for=condition=available --timeout=300s deployment/asset-service
kubectl wait --for=condition=available --timeout=300s deployment/apisix-gateway

# Show deployment status
echo "📊 Deployment status:"
kubectl get pods -l app=trading-service
kubectl get pods -l app=asset-service
kubectl get pods -l app=apisix-gateway

echo "🌐 Services:"
kubectl get services

echo "✅ Deployment completed!"
