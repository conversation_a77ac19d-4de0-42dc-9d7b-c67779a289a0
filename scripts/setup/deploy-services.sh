#!/bin/bash

# Deploy all services to Kubernetes
set -e

echo "🚀 Deploying services to Kubernetes..."

# Deploy service accounts
echo "👤 Creating service accounts..."
kubectl apply -f k8s/manifests/trading/serviceaccount.yaml
kubectl apply -f k8s/manifests/asset/serviceaccount.yaml
kubectl apply -f k8s/manifests/apisix/serviceaccount.yaml

# Deploy services
echo "🌐 Creating services..."
kubectl apply -f k8s/manifests/trading/service.yaml
kubectl apply -f k8s/manifests/asset/service.yaml
kubectl apply -f k8s/manifests/apisix/service.yaml

# Deploy Apisix configuration
echo "⚙️  Creating Apisix configuration..."
kubectl apply -f k8s/manifests/apisix/configmap.yaml

# Deploy applications
echo "📦 Deploying applications..."
kubectl apply -f k8s/manifests/trading/deployment.yaml
kubectl apply -f k8s/manifests/asset/deployment.yaml
kubectl apply -f k8s/manifests/apisix/deployment.yaml

# Wait for deployments to be ready
echo "⏳ Waiting for deployments to be ready..."
kubectl wait --for=condition=available --timeout=300s deployment/trading-service
kubectl wait --for=condition=available --timeout=300s deployment/asset-service
kubectl wait --for=condition=available --timeout=300s deployment/apisix-gateway

# Deploy Istio configurations
echo "🔒 Applying Istio security policies..."
kubectl apply -f k8s/istio/policies/peer-authentication.yaml # TODO
kubectl apply -f k8s/istio/policies/authorization-policy.yaml # TODO

echo "🌐 Applying Istio traffic management..."
kubectl apply -f k8s/istio/gateways/destination-rules.yaml
kubectl apply -f k8s/istio/gateways/virtual-services.yaml

# Show deployment status
echo "📊 Deployment status:"
kubectl get pods -l app=trading-service
kubectl get pods -l app=asset-service
kubectl get pods -l app=apisix-gateway

echo "🌐 Services:"
kubectl get services

echo "✅ Deployment completed!"
echo "📋 Next steps:"
echo "   1. Run ./test-system.sh to test the system"
echo "   2. Check logs: kubectl logs -l app=<service-name>"
echo "   3. Access gateway: kubectl port-forward svc/apisix-gateway 8080:9080"
