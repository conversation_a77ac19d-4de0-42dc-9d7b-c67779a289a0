#!/bin/bash

# Generate certificates for webhook server only
set -e

echo "🔐 Generating certificates for webhook server..."

# Create certificate directory for webhook
WEBHOOK_CERT_DIR="sidecar-injector/certs"
mkdir -p $WEBHOOK_CERT_DIR

# Generate CA private key for webhook certificates
echo "📝 Generating CA private key..."
openssl genrsa -out $WEBHOOK_CERT_DIR/ca.key 4096

# Generate CA certificate
echo "📝 Generating CA certificate..."
openssl req -new -x509 -key $WEBHOOK_CERT_DIR/ca.key -sha256 -subj "/C=US/ST=CA/O=K8sEnvoy/CN=k8s-envoy-webhook-ca" -days 3650 -out $WEBHOOK_CERT_DIR/ca.crt

# Generate webhook private key
echo "📝 Generating webhook private key..."
openssl genrsa -out $WEBHOOK_CERT_DIR/tls.key 4096

# Create webhook certificate signing request
echo "📝 Creating webhook certificate signing request..."
cat > $WEBHOOK_CERT_DIR/webhook.conf <<EOF
[req]
default_bits = 4096
prompt = no
distinguished_name = req_distinguished_name
req_extensions = req_ext

[req_distinguished_name]
C = US
ST = CA
O = K8sEnvoy
CN = sidecar-injector-webhook

[req_ext]
subjectAltName = @alt_names

[alt_names]
DNS.1 = sidecar-injector-webhook
DNS.2 = sidecar-injector-webhook.default.svc
DNS.3 = sidecar-injector-webhook.default.svc.cluster.local
EOF

openssl req -new -key $WEBHOOK_CERT_DIR/tls.key -out $WEBHOOK_CERT_DIR/webhook.csr -config $WEBHOOK_CERT_DIR/webhook.conf

# Generate webhook certificate signed by CA
echo "📝 Generating webhook certificate..."
openssl x509 -req -in $WEBHOOK_CERT_DIR/webhook.csr -CA $WEBHOOK_CERT_DIR/ca.crt -CAkey $WEBHOOK_CERT_DIR/ca.key -CAcreateserial -out $WEBHOOK_CERT_DIR/tls.crt -days 365 -extensions req_ext -extfile $WEBHOOK_CERT_DIR/webhook.conf

# Clean up CSR files
rm $WEBHOOK_CERT_DIR/webhook.csr $WEBHOOK_CERT_DIR/webhook.conf

echo "✅ Webhook certificates generated successfully!"
echo "📁 Webhook certificates: $WEBHOOK_CERT_DIR/"

# Display certificate information
echo "🔍 Certificate information:"
echo "CA Certificate:"
openssl x509 -in $WEBHOOK_CERT_DIR/ca.crt -text -noout | grep -A 2 "Subject:"

echo "Webhook Certificate:"
openssl x509 -in $WEBHOOK_CERT_DIR/tls.crt -text -noout | grep -A 2 "Subject:"