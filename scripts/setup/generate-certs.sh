#!/bin/bash

# Generate certificates for mTLS communication
set -e

echo "🔐 Generating certificates for mTLS..."

CERT_DIR="envoy/certs"
mkdir -p $CERT_DIR

# Generate CA private key
echo "📝 Generating CA private key..."
openssl genrsa -out $CERT_DIR/ca.key 4096

# Generate CA certificate
echo "📝 Generating CA certificate..."
openssl req -new -x509 -key $CERT_DIR/ca.key -sha256 -subj "/C=US/ST=CA/O=K8sEnvoy/CN=k8s-envoy-ca" -days 3650 -out $CERT_DIR/ca.crt

# Generate server private key
echo "📝 Generating server private key..."
openssl genrsa -out $CERT_DIR/tls.key 4096

# Create certificate signing request
echo "📝 Creating certificate signing request..."
cat > $CERT_DIR/server.conf <<EOF
[req]
default_bits = 4096
prompt = no
distinguished_name = req_distinguished_name
req_extensions = req_ext

[req_distinguished_name]
C = US
ST = CA
O = K8sEnvoy
CN = envoy-service

[req_ext]
subjectAltName = @alt_names

[alt_names]
DNS.1 = localhost
DNS.2 = envoy-service
DNS.3 = trading-service
DNS.4 = asset-service
DNS.5 = trading-service.default.svc.cluster.local
DNS.6 = asset-service.default.svc.cluster.local
DNS.7 = *.default.svc.cluster.local
DNS.8 = *.svc.cluster.local
IP.1 = 127.0.0.1
IP.2 = ********
EOF

openssl req -new -key $CERT_DIR/tls.key -out $CERT_DIR/server.csr -config $CERT_DIR/server.conf

# Generate server certificate signed by CA
echo "📝 Generating server certificate..."
openssl x509 -req -in $CERT_DIR/server.csr -CA $CERT_DIR/ca.crt -CAkey $CERT_DIR/ca.key -CAcreateserial -out $CERT_DIR/tls.crt -days 365 -extensions req_ext -extfile $CERT_DIR/server.conf

# Generate webhook certificates
echo "📝 Generating webhook certificates..."
WEBHOOK_CERT_DIR="sidecar-injector/certs"
mkdir -p $WEBHOOK_CERT_DIR

# Generate webhook private key
openssl genrsa -out $WEBHOOK_CERT_DIR/tls.key 4096

# Create webhook certificate signing request
cat > $WEBHOOK_CERT_DIR/webhook.conf <<EOF
[req]
default_bits = 4096
prompt = no
distinguished_name = req_distinguished_name
req_extensions = req_ext

[req_distinguished_name]
C = US
ST = CA
O = K8sEnvoy
CN = sidecar-injector-webhook

[req_ext]
subjectAltName = @alt_names

[alt_names]
DNS.1 = sidecar-injector-webhook
DNS.2 = sidecar-injector-webhook.default.svc
DNS.3 = sidecar-injector-webhook.default.svc.cluster.local
EOF

openssl req -new -key $WEBHOOK_CERT_DIR/tls.key -out $WEBHOOK_CERT_DIR/webhook.csr -config $WEBHOOK_CERT_DIR/webhook.conf

# Generate webhook certificate signed by CA
openssl x509 -req -in $WEBHOOK_CERT_DIR/webhook.csr -CA $CERT_DIR/ca.crt -CAkey $CERT_DIR/ca.key -CAcreateserial -out $WEBHOOK_CERT_DIR/tls.crt -days 365 -extensions req_ext -extfile $WEBHOOK_CERT_DIR/webhook.conf

# Clean up CSR files
rm $CERT_DIR/server.csr $CERT_DIR/server.conf
rm $WEBHOOK_CERT_DIR/webhook.csr $WEBHOOK_CERT_DIR/webhook.conf

echo "✅ Certificates generated successfully!"
echo "📁 Envoy certificates: $CERT_DIR/"
echo "📁 Webhook certificates: $WEBHOOK_CERT_DIR/"

# Display certificate information
echo "🔍 Certificate information:"
echo "CA Certificate:"
openssl x509 -in $CERT_DIR/ca.crt -text -noout | grep -A 2 "Subject:"

echo "Server Certificate:"
openssl x509 -in $CERT_DIR/tls.crt -text -noout | grep -A 2 "Subject:"
openssl x509 -in $CERT_DIR/tls.crt -text -noout | grep -A 10 "Subject Alternative Name"

echo "Webhook Certificate:"
openssl x509 -in $WEBHOOK_CERT_DIR/tls.crt -text -noout | grep -A 2 "Subject:"

echo "📋 Next steps:"
echo "   1. Create Kubernetes secrets with these certificates"
echo "   2. Deploy the sidecar injector webhook"
echo "   3. Deploy services with sidecar injection enabled"
