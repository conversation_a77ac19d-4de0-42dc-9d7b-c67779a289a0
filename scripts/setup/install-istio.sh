#!/bin/bash

# Install Istio setup script
set -e

echo "🚀 Installing Istio..."

# Check if istioctl is installed
if ! command -v istioctl &> /dev/null; then
    echo "📥 Downloading and installing istioctl..."
    curl -L https://istio.io/downloadIstio | sh -
    export PATH="$PWD/istio-*/bin:$PATH"
    echo "✅ istioctl installed"
else
    echo "✅ istioctl already installed"
fi

# Install Istio
echo "📦 Installing Istio with minimal profile..."
# only install istiod for envoy dashboard.
istioctl install --set profile=minimal -y

# Enable Istio injection for default namespace
echo "🔧 Enabling Istio sidecar injection for default namespace..."
kubectl label namespace default istio-injection=enabled --overwrite

# Verify installation
echo "🔍 Verifying Istio installation..."
kubectl get pods -n istio-system

echo "✅ Istio installation completed!"
echo "📋 Next steps:"
echo "   1. Build and push your service images"
echo "   2. Run ./deploy-services.sh to deploy the services"
echo "   3. Run ./test-system.sh to test the system"
