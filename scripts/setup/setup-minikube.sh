#!/bin/bash

# Setup minikube for the k8s-envoy project
set -e

echo "🚀 Setting up minikube for k8s-envoy project..."

# Check if minikube is installed
if ! command -v minikube &> /dev/null; then
    echo "❌ minikube is not installed. Please install minikube first."
    echo "   Visit: https://minikube.sigs.k8s.io/docs/start/"
    exit 1
fi

# Check if kubectl is installed
if ! command -v kubectl &> /dev/null; then
    echo "❌ kubectl is not installed. Please install kubectl first."
    echo "   Visit: https://kubernetes.io/docs/tasks/tools/"
    exit 1
fi

# Start minikube with appropriate resources
echo "🔧 Starting minikube..."
minikube start \
    --cpus=4 \
    --memory=8192 \
    --disk-size=20g \
    --driver=docker \
    --kubernetes-version=v1.28.0

# Enable necessary addons
echo "🔌 Enabling minikube addons..."
minikube addons enable ingress
minikube addons enable metrics-server
minikube addons enable dashboard

# Verify cluster is ready
echo "🔍 Verifying cluster status..."
kubectl cluster-info
kubectl get nodes

echo "✅ Minikube setup completed!"
echo "📋 Cluster information:"
kubectl get nodes -o wide_
