#!/bin/bash

# Test script for the custom access-control plugin
# This script tests various access control scenarios

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🧪 Testing Custom Access Control Plugin${NC}"
echo "=========================================="

# Get Apisix service URL
APISIX_URL="http://$(minikube ip):$(kubectl get service apisix-gateway -o jsonpath='{.spec.ports[0].nodePort}')"
echo "🌐 Apisix URL: $APISIX_URL"

# Test function
test_access() {
    local description="$1"
    local uri="$2"
    local method="${3:-GET}"
    local jwt_token="${4:-}"
    local expected_status="${5:-200}"
    
    echo -e "\n${YELLOW}Testing: $description${NC}"
    echo "URI: $method $uri"
    echo "JWT: ${jwt_token:-none}"
    echo "Expected: HTTP $expected_status"
    
    local curl_cmd="curl -s -w '%{http_code}' -X $method"
    
    if [ -n "$jwt_token" ]; then
        curl_cmd="$curl_cmd -H 'Authorization: Bearer $jwt_token'"
    fi
    
    curl_cmd="$curl_cmd $APISIX_URL$uri"
    
    local response=$(eval $curl_cmd)
    local status_code="${response: -3}"
    local body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS: Got HTTP $status_code${NC}"
    else
        echo -e "${RED}❌ FAIL: Expected HTTP $expected_status, got HTTP $status_code${NC}"
        if [ -n "$body" ]; then
            echo "Response body: $body"
        fi
    fi
}

# Simulate JWT tokens for different services
TRADING_JWT="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.trading-service-payload.signature"
ASSET_JWT="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.asset-service-payload.signature"
UNKNOWN_JWT="eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.unknown-service-payload.signature"

echo -e "\n${BLUE}📋 Test Plan:${NC}"
echo "1. Trading service accessing trading endpoints (should pass)"
echo "2. Trading service accessing allowed asset endpoints (should pass)"
echo "3. Trading service accessing denied asset endpoints (should fail)"
echo "4. Asset service accessing asset endpoints (should pass)"
echo "5. Asset service accessing allowed trading endpoints (should pass)"
echo "6. Asset service accessing denied trading endpoints (should fail)"
echo "7. Unknown service accessing any endpoint (should fail)"
echo "8. No JWT token provided (should fail)"

# Wait for system to be ready
echo -e "\n${YELLOW}⏳ Waiting for system to be ready...${NC}"
sleep 5

# Test 1: Trading service accessing trading endpoints
test_access "Trading service → trading endpoints" "/trading/trades" "GET" "$TRADING_JWT" "200"

# Test 2: Trading service accessing allowed asset endpoints
test_access "Trading service → asset prices" "/asset/prices" "GET" "$TRADING_JWT" "200"
test_access "Trading service → market data" "/asset/market-data" "GET" "$TRADING_JWT" "200"
test_access "Trading service → specific asset" "/asset/assets/AAPL" "GET" "$TRADING_JWT" "200"

# Test 3: Trading service accessing denied asset endpoints
test_access "Trading service → list all assets (denied)" "/asset/assets" "GET" "$TRADING_JWT" "403"
test_access "Trading service → update asset price (denied)" "/asset/assets/AAPL/price" "PUT" "$TRADING_JWT" "403"

# Test 4: Asset service accessing asset endpoints
test_access "Asset service → asset endpoints" "/asset/assets" "GET" "$ASSET_JWT" "200"

# Test 5: Asset service accessing allowed trading endpoints
test_access "Asset service → read trades" "/trading/trades" "GET" "$ASSET_JWT" "200"
test_access "Asset service → trades by symbol" "/trading/trades/symbol/AAPL" "GET" "$ASSET_JWT" "200"
test_access "Asset service → trading stats" "/trading/stats" "GET" "$ASSET_JWT" "200"

# Test 6: Asset service accessing denied trading endpoints
test_access "Asset service → create trade (denied)" "/trading/trades" "POST" "$ASSET_JWT" "403"

# Test 7: Unknown service accessing any endpoint
test_access "Unknown service → any endpoint" "/trading/trades" "GET" "$UNKNOWN_JWT" "403"

# Test 8: No JWT token provided
test_access "No JWT token → any endpoint" "/trading/trades" "GET" "" "403"

echo -e "\n${BLUE}🏁 Access Control Plugin Tests Complete${NC}"
echo "Check the results above to verify plugin behavior."
