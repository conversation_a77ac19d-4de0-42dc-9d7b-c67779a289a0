#!/bin/bash

# Test script for System Architecture
# Tests the complete flow: Service -> Envoy -> Apisix -> Target Service

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
NAMESPACE="default"
APISIX_SERVICE="apisix-gateway"
TRADING_SERVICE="trading-service"
ASSET_SERVICE="asset-service"

echo -e "${BLUE}🧪 Testing System Architecture${NC}"
echo "=================================================="

# Function to test endpoint
test_endpoint() {
    local method=$1
    local service=$2
    local path=$3
    local data=$4
    local expected_status=$5
    local description=$6
    
    echo -e "\n${YELLOW}Testing: $description${NC}"
    echo "Method: $method, Service: $service, Path: $path"
    
    if [ -n "$data" ]; then
        echo "Data: $data"
    fi
    
    # Use kubectl exec to test from within a pod (simulating internal traffic)
    local cmd="curl -s -w '%{http_code}' -o /tmp/response"
    
    if [ "$method" = "POST" ] && [ -n "$data" ]; then
        cmd="$cmd -X POST -H 'Content-Type: application/json' -d '$data'"
    fi
    
    cmd="$cmd http://$service:8010$path"
    
    local response_code
    response_code=$(kubectl exec -n $NAMESPACE deployment/$TRADING_SERVICE -c trading-service -- sh -c "$cmd" 2>/dev/null || echo "000")
    
    if [ "$response_code" = "$expected_status" ]; then
        echo -e "${GREEN}✅ PASS${NC} - Status: $response_code"
        if [ -f /tmp/response ]; then
            echo "Response: $(kubectl exec -n $NAMESPACE deployment/$TRADING_SERVICE -c trading-service -- cat /tmp/response 2>/dev/null | head -c 200)"
        fi
    else
        echo -e "${RED}❌ FAIL${NC} - Expected: $expected_status, Got: $response_code"
        if [ -f /tmp/response ]; then
            echo "Response: $(kubectl exec -n $NAMESPACE deployment/$TRADING_SERVICE -c trading-service -- cat /tmp/response 2>/dev/null)"
        fi
    fi
}

# Function to test Apisix routing
test_apisix_routing() {
    echo -e "\n${BLUE}📡 Testing Apisix Gateway Routing${NC}"
    echo "============================================"
    
    # Test direct access to Apisix (should work)
    echo -e "\n${YELLOW}Testing direct Apisix access${NC}"
    kubectl exec -n $NAMESPACE deployment/$TRADING_SERVICE -c trading-service -- \
        curl -s -w '%{http_code}' -o /dev/null \
        http://apisix-gateway.default.svc.cluster.local/asset/assets || echo "Failed to reach Apisix"
    
    # Test routing through Apisix
    test_endpoint "GET" "apisix-gateway.default.svc.cluster.local" "/asset/assets" "" "200" "Asset list via Apisix"
    test_endpoint "GET" "apisix-gateway.default.svc.cluster.local" "/trading/trades" "" "200" "Trading list via Apisix"
    test_endpoint "GET" "apisix-gateway.default.svc.cluster.local" "/asset/prices" "" "200" "Asset prices via Apisix"
}

# Function to test access control
test_access_control() {
    echo -e "\n${BLUE}🔒 Testing Access Control Policies${NC}"
    echo "=========================================="
    
    # Test allowed access
    test_endpoint "GET" "apisix-gateway.default.svc.cluster.local" "/asset/prices" "" "200" "Trading service accessing asset prices (ALLOWED)"
    test_endpoint "GET" "apisix-gateway.default.svc.cluster.local" "/asset/market-data" "" "200" "Trading service accessing market data (ALLOWED)"
    
    # Test denied access (these should fail with 403)
    test_endpoint "PUT" "apisix-gateway.default.svc.cluster.local" "/asset/assets/AAPL/price" '{"price": 200}' "403" "Trading service updating asset price (DENIED)"
    test_endpoint "POST" "apisix-gateway.default.svc.cluster.local" "/trading/trades" '{"symbol":"TEST","quantity":1,"price":100,"side":"buy"}' "403" "Asset service creating trade (DENIED)"
}

# Function to test service communication
test_service_communication() {
    echo -e "\n${BLUE}🔄 Testing Service-to-Service Communication${NC}"
    echo "=============================================="
    
    # Test trading service calling asset service via Apisix
    test_endpoint "GET" "$TRADING_SERVICE" "/market-data" "" "200" "Trading service getting market data from asset service"
    test_endpoint "GET" "$TRADING_SERVICE" "/prices" "" "200" "Trading service getting prices from asset service"
    
    # Test creating a trade (which validates asset via Apisix)
    test_endpoint "POST" "$TRADING_SERVICE" "/trades" '{"symbol":"AAPL","quantity":10,"price":150.0,"side":"buy"}' "201" "Creating trade with asset validation"
}

# Function to test external access
test_external_access() {
    echo -e "\n${BLUE}🌐 Testing External Internet Access${NC}"
    echo "======================================="
    
    # Test external access from trading service (should bypass Apisix)
    echo -e "\n${YELLOW}Testing external HTTP access${NC}"
    kubectl exec -n $NAMESPACE deployment/$TRADING_SERVICE -c trading-service -- \
        curl -s -w '%{http_code}' -o /dev/null --max-time 10 \
        http://httpbin.org/get || echo "External access test failed"
}

# Main test execution
main() {
    echo -e "${BLUE}Starting System Tests...${NC}\n"
    
    # Wait for pods to be ready
    echo -e "\n${YELLOW}Waiting for pods to be ready...${NC}"
    kubectl wait --for=condition=ready pod -l app=apisix-gateway -n $NAMESPACE --timeout=60s
    kubectl wait --for=condition=ready pod -l app=trading-service -n $NAMESPACE --timeout=60s
    kubectl wait --for=condition=ready pod -l app=asset-service -n $NAMESPACE --timeout=60s
    
    # Run tests
    test_apisix_routing
    test_service_communication
    test_access_control
    test_external_access
    
    echo -e "\n${BLUE}🎉 System Tests Completed!${NC}"
    echo "=================================================="
}

# Run main function
main "$@"
