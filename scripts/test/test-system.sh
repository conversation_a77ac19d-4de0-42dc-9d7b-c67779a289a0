#!/bin/bash

# Test the deployed system
set -e

echo "🧪 Testing the k8s-envoy system..."

# Get Apisix gateway service details
GATEWAY_SERVICE=$(kubectl get svc apisix-gateway -o jsonpath='{.status.loadBalancer.ingress[0].ip}')
if [ -z "$GATEWAY_SERVICE" ]; then
    GATEWAY_SERVICE=$(kubectl get svc apisix-gateway -o jsonpath='{.status.loadBalancer.ingress[0].hostname}')
fi

if [ -z "$GATEWAY_SERVICE" ]; then
    echo "⚠️  LoadBalancer not available, using port-forward..."
    kubectl port-forward svc/apisix-gateway 8080:9080 &
    PORT_FORWARD_PID=$!
    sleep 5
    GATEWAY_URL="http://localhost:8080"
else
    GATEWAY_URL="http://$GATEWAY_SERVICE:9080"
fi

echo "🌐 Gateway URL: $GATEWAY_URL"

# Test functions
test_endpoint() {
    local method=$1
    local endpoint=$2
    local data=$3
    local expected_status=$4
    
    echo "🔍 Testing $method $endpoint"
    
    if [ -n "$data" ]; then
        response=$(curl -s -w "%{http_code}" -X $method \
            -H "Content-Type: application/json" \
            -d "$data" \
            "$GATEWAY_URL$endpoint")
    else
        response=$(curl -s -w "%{http_code}" -X $method "$GATEWAY_URL$endpoint")
    fi
    
    status_code="${response: -3}"
    body="${response%???}"
    
    if [ "$status_code" = "$expected_status" ]; then
        echo "✅ $method $endpoint - Status: $status_code"
        if [ -n "$body" ] && [ "$body" != "null" ]; then
            echo "   Response: $(echo $body | jq -c . 2>/dev/null || echo $body)"
        fi
    else
        echo "❌ $method $endpoint - Expected: $expected_status, Got: $status_code"
        echo "   Response: $body"
        return 1
    fi
}

# Test asset service
echo "📊 Testing Asset Service..."
test_endpoint "GET" "/api/assets/health" "" "200"
test_endpoint "GET" "/api/assets/assets" "" "200"
test_endpoint "GET" "/api/assets/assets/AAPL" "" "200"
test_endpoint "GET" "/api/assets/assets/NONEXISTENT" "" "404"

# Create a new asset
echo "📝 Creating new asset..."
new_asset='{"symbol":"TEST","name":"Test Asset","type":"stock","exchange":"TEST","currency":"USD","current_price":100.0}'
test_endpoint "POST" "/api/assets/assets" "$new_asset" "201"

# Test trading service
echo "💰 Testing Trading Service..."
test_endpoint "GET" "/api/trading/health" "" "200"
test_endpoint "GET" "/api/trading/trades" "" "200"

# Create a new trade
echo "📈 Creating new trade..."
new_trade='{"symbol":"AAPL","quantity":10,"price":150.0,"side":"buy"}'
test_endpoint "POST" "/api/trading/trades" "$new_trade" "201"

# Test invalid trade
echo "❌ Testing invalid trade..."
invalid_trade='{"symbol":"INVALID","quantity":10,"price":150.0,"side":"buy"}'
test_endpoint "POST" "/api/trading/trades" "$invalid_trade" "400"

# Test stats endpoints
echo "📊 Testing stats endpoints..."
test_endpoint "GET" "/api/assets/stats" "" "200"
test_endpoint "GET" "/api/trading/stats" "" "200"

# Test health endpoint
echo "❤️  Testing health endpoint..."
test_endpoint "GET" "/health" "" "200"

# Cleanup port-forward if used
if [ -n "$PORT_FORWARD_PID" ]; then
    kill $PORT_FORWARD_PID 2>/dev/null || true
fi

echo "✅ System testing completed!"
echo "📋 Summary:"
echo "   - Asset service: Working"
echo "   - Trading service: Working"
echo "   - Apisix gateway: Working"
echo "   - Service-to-service communication: Working"
echo "   - mTLS and authorization: Configured"

echo "🔍 To check Istio mTLS status:"
echo "   kubectl exec -it deployment/trading-service -c istio-proxy -- openssl s_client -connect asset-service:8080"

echo "📊 To view Istio metrics:"
echo "   kubectl port-forward -n istio-system svc/kiali 20001:20001"
echo "   kubectl port-forward -n istio-system svc/grafana 3000:3000"
