FROM python:3.11-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    curl tcpdump net-tools sudo \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY app/ ./app/

# Expose port
EXPOSE 8010

# use root for debugging purposes
USER root

# Run the application
CMD ["gunicorn", "--bind", "0.0.0.0:8010", "--workers", "2", "--timeout", "30", "app.main:app"]
