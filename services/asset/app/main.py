#!/usr/bin/env python3
"""
Asset Service - A simple REST API for asset management
"""

import os
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
SERVICE_NAME = 'asset-service'
SERVICE_VERSION = '1.0.0'

# In-memory storage for demo purposes - pre-populated with some assets
assets = {
    'AAPL': {
        'symbol': 'AAPL',
        'name': 'Apple Inc.',
        'type': 'stock',
        'exchange': 'NASDAQ',
        'currency': 'USD',
        'current_price': 150.25,
        'last_updated': datetime.utcnow().isoformat()
    },
    'GOOGL': {
        'symbol': 'GOOGL',
        'name': 'Alphabet Inc.',
        'type': 'stock',
        'exchange': 'NASDAQ',
        'currency': 'USD',
        'current_price': 2750.80,
        'last_updated': datetime.utcnow().isoformat()
    },
    'TSLA': {
        'symbol': 'TSLA',
        'name': 'Tesla Inc.',
        'type': 'stock',
        'exchange': 'NASDAQ',
        'currency': 'USD',
        'current_price': 245.67,
        'last_updated': datetime.utcnow().isoformat()
    },
    'BTC': {
        'symbol': 'BTC',
        'name': 'Bitcoin',
        'type': 'cryptocurrency',
        'exchange': 'crypto',
        'currency': 'USD',
        'current_price': 45000.00,
        'last_updated': datetime.utcnow().isoformat()
    },
    'ETH': {
        'symbol': 'ETH',
        'name': 'Ethereum',
        'type': 'cryptocurrency',
        'exchange': 'crypto',
        'currency': 'USD',
        'current_price': 3200.50,
        'last_updated': datetime.utcnow().isoformat()
    }
}

@app.route('/assets', methods=['GET'])
def get_assets():
    """Get all assets"""
    logger.info("Fetching all assets")
    
    # Optional filtering by type
    asset_type = request.args.get('type')
    if asset_type:
        filtered_assets = {k: v for k, v in assets.items() if v['type'] == asset_type}
        return jsonify({
            'assets': list(filtered_assets.values()),
            'count': len(filtered_assets),
            'filter': {'type': asset_type}
        })
    
    return jsonify({
        'assets': list(assets.values()),
        'count': len(assets)
    })

@app.route('/assets/<symbol>', methods=['GET'])
def get_asset(symbol):
    """Get a specific asset by symbol"""
    symbol = symbol.upper()
    asset = assets.get(symbol)
    
    if not asset:
        return jsonify({'error': f'Asset {symbol} not found'}), 404
    
    logger.info(f"Fetching asset: {symbol}")
    return jsonify(asset)

@app.route('/assets', methods=['POST'])
def create_asset():
    """Create a new asset"""
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    # Validate required fields
    required_fields = ['symbol', 'name', 'type', 'exchange', 'currency', 'current_price']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'Missing required field: {field}'}), 400
    
    symbol = data['symbol'].upper()
    
    # Check if asset already exists
    if symbol in assets:
        return jsonify({'error': f'Asset {symbol} already exists'}), 409
    
    # Validate asset type
    valid_types = ['stock', 'bond', 'cryptocurrency', 'commodity', 'forex']
    if data['type'] not in valid_types:
        return jsonify({'error': f'Invalid asset type. Must be one of: {valid_types}'}), 400
    
    # Create asset
    asset = {
        'symbol': symbol,
        'name': data['name'],
        'type': data['type'],
        'exchange': data['exchange'],
        'currency': data['currency'],
        'current_price': float(data['current_price']),
        'last_updated': datetime.utcnow().isoformat()
    }
    
    assets[symbol] = asset
    
    logger.info(f"Created asset: {symbol} - {asset['name']}")
    
    return jsonify(asset), 201

@app.route('/assets/<symbol>', methods=['PUT'])
def update_asset(symbol):
    """Update an existing asset"""
    symbol = symbol.upper()
    
    if symbol not in assets:
        return jsonify({'error': f'Asset {symbol} not found'}), 404
    
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    # Update allowed fields
    allowed_fields = ['name', 'type', 'exchange', 'currency', 'current_price']
    updated_asset = assets[symbol].copy()
    
    for field in allowed_fields:
        if field in data:
            if field == 'current_price':
                updated_asset[field] = float(data[field])
            else:
                updated_asset[field] = data[field]
    
    updated_asset['last_updated'] = datetime.utcnow().isoformat()
    assets[symbol] = updated_asset
    
    logger.info(f"Updated asset: {symbol}")
    
    return jsonify(updated_asset)

@app.route('/assets/<symbol>/price', methods=['PUT'])
def update_asset_price(symbol):
    """Update asset price"""
    symbol = symbol.upper()
    
    if symbol not in assets:
        return jsonify({'error': f'Asset {symbol} not found'}), 404
    
    data = request.get_json()
    if not data or 'price' not in data:
        return jsonify({'error': 'Price is required'}), 400
    
    try:
        new_price = float(data['price'])
        if new_price <= 0:
            return jsonify({'error': 'Price must be positive'}), 400
    except (ValueError, TypeError):
        return jsonify({'error': 'Invalid price format'}), 400
    
    assets[symbol]['current_price'] = new_price
    assets[symbol]['last_updated'] = datetime.utcnow().isoformat()
    
    logger.info(f"Updated price for {symbol}: {new_price}")
    
    return jsonify({
        'symbol': symbol,
        'new_price': new_price,
        'updated_at': assets[symbol]['last_updated']
    })

@app.route('/assets/types', methods=['GET'])
def get_asset_types():
    """Get available asset types"""
    types = list(set(asset['type'] for asset in assets.values()))
    return jsonify({
        'types': sorted(types),
        'count': len(types)
    })

@app.route('/stats', methods=['GET'])
def get_stats():
    """Get asset statistics"""
    if not assets:
        return jsonify({
            'total_assets': 0,
            'types': [],
            'exchanges': []
        })
    
    types = list(set(asset['type'] for asset in assets.values()))
    exchanges = list(set(asset['exchange'] for asset in assets.values()))
    
    return jsonify({
        'total_assets': len(assets),
        'types': sorted(types),
        'exchanges': sorted(exchanges),
        'service_info': {
            'name': SERVICE_NAME,
            'version': SERVICE_VERSION
        }
    })

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8080))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Starting {SERVICE_NAME} v{SERVICE_VERSION} on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
