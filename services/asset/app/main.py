#!/usr/bin/env python3
"""
Asset Service - A simple REST API for asset management
"""

import os
import json
import logging
from datetime import datetime
from flask import Flask, request
from flask_cors import CORS

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
SERVICE_NAME = 'asset-service'
SERVICE_VERSION = '1.0.0'

@app.route('/asset/v1/echo', methods=['POST'])
def echo():
    data = request.get_json()
    return "echo from asset: " + data

@app.route('/asset/v1/hello', methods=['GET'])
def get_prices():
    return "Hello from asset service!"

@app.route('/asset/v1/hi', methods=['GET'])
def get_prices():
    return "Hi from asset service!"


if __name__ == '__main__':
    port = int(os.getenv('PORT', 8010))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Starting {SERVICE_NAME} v{SERVICE_VERSION} on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
