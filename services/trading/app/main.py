#!/usr/bin/env python3
"""
Trading Service - A simple REST API for trading operations
"""

import os
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS
import requests

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
ASSET_SERVICE_URL = os.getenv('ASSET_SERVICE_URL', 'http://asset-service:8080')
SERVICE_NAME = 'trading-service'
SERVICE_VERSION = '1.0.0'

# In-memory storage for demo purposes
trades = []
trade_counter = 1

@app.route('/trades', methods=['GET'])
def get_trades():
    """Get all trades"""
    logger.info("Fetching all trades")
    return jsonify({
        'trades': trades,
        'count': len(trades)
    })

@app.route('/trades', methods=['POST'])
def create_trade():
    """Create a new trade"""
    global trade_counter
    
    data = request.get_json()
    if not data:
        return jsonify({'error': 'No data provided'}), 400
    
    # Validate required fields
    required_fields = ['symbol', 'quantity', 'price', 'side']
    for field in required_fields:
        if field not in data:
            return jsonify({'error': f'Missing required field: {field}'}), 400
    
    # Validate side
    if data['side'] not in ['buy', 'sell']:
        return jsonify({'error': 'Side must be either "buy" or "sell"'}), 400
    
    # Validate asset exists by calling asset service
    try:
        asset_response = requests.get(f"{ASSET_SERVICE_URL}/assets/{data['symbol']}", timeout=5)
        if asset_response.status_code == 404:
            return jsonify({'error': f'Asset {data["symbol"]} not found'}), 400
        elif asset_response.status_code != 200:
            logger.warning(f"Asset service returned status {asset_response.status_code}")
    except requests.exceptions.RequestException as e:
        logger.warning(f"Could not validate asset with asset service: {e}")
        # Continue without validation in case asset service is down
    
    # Create trade
    trade = {
        'id': trade_counter,
        'symbol': data['symbol'],
        'quantity': float(data['quantity']),
        'price': float(data['price']),
        'side': data['side'],
        'timestamp': datetime.utcnow().isoformat(),
        'status': 'executed'
    }
    
    trades.append(trade)
    trade_counter += 1
    
    logger.info(f"Created trade: {trade['id']} - {trade['side']} {trade['quantity']} {trade['symbol']} @ {trade['price']}")
    
    return jsonify(trade), 201

@app.route('/trades/<int:trade_id>', methods=['GET'])
def get_trade(trade_id):
    """Get a specific trade by ID"""
    trade = next((t for t in trades if t['id'] == trade_id), None)
    if not trade:
        return jsonify({'error': 'Trade not found'}), 404
    
    return jsonify(trade)

@app.route('/trades/symbol/<symbol>', methods=['GET'])
def get_trades_by_symbol(symbol):
    """Get trades for a specific symbol"""
    symbol_trades = [t for t in trades if t['symbol'].upper() == symbol.upper()]
    return jsonify({
        'symbol': symbol.upper(),
        'trades': symbol_trades,
        'count': len(symbol_trades)
    })

@app.route('/stats', methods=['GET'])
def get_stats():
    """Get trading statistics"""
    if not trades:
        return jsonify({
            'total_trades': 0,
            'total_volume': 0,
            'symbols': []
        })
    
    total_volume = sum(t['quantity'] * t['price'] for t in trades)
    symbols = list(set(t['symbol'] for t in trades))
    
    return jsonify({
        'total_trades': len(trades),
        'total_volume': total_volume,
        'symbols': symbols,
        'service_info': {
            'name': SERVICE_NAME,
            'version': SERVICE_VERSION
        }
    })

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8080))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Starting {SERVICE_NAME} v{SERVICE_VERSION} on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
