#!/usr/bin/env python3
"""
Trading Service - A simple REST API for trading operations
"""

import os
import json
import logging
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS
import requests

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)
CORS(app)

# Configuration
# Legacy system: all internal traffic goes through Apisix gateway
APISIX_GATEWAY_URL = os.getenv('APISIX_GATEWAY_URL', 'http://apisix-gateway.default.svc.cluster.local:80')
ASSET_SERVICE_URL = f"{APISIX_GATEWAY_URL}/asset"  # Route through Apisix with /asset prefix
SERVICE_NAME = 'trading-service'
SERVICE_VERSION = '1.0.0'

@app.route('/trading/v1/echo', methods=['POST'])
def echo():
    data = request.get_json()
    return "echo from trading: " + data

@app.route('/trading/v1/hello', methods=['GET'])
def get_prices():
    return "Hello from trading service!"

@app.route('/trading/v1/hi', methods=['GET'])
def get_prices():
    return "Hi from trading service!"

if __name__ == '__main__':
    port = int(os.getenv('PORT', 8080))
    debug = os.getenv('DEBUG', 'false').lower() == 'true'
    
    logger.info(f"Starting {SERVICE_NAME} v{SERVICE_VERSION} on port {port}")
    app.run(host='0.0.0.0', port=port, debug=debug)
