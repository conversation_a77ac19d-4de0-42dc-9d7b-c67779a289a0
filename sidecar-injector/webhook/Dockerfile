FROM golang:1.21-alpine AS builder

WORKDIR /app

# Copy go mod files
COPY go.mod go.sum ./
RUN go mod download

# Copy source code
COPY main.go ./

# Build the binary with proper flags
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -a -installsuffix cgo -ldflags '-extldflags "-static"' -o webhook .

# Final stage
FROM alpine:latest

RUN apk --no-cache add ca-certificates
WORKDIR /app

# Copy the binary from builder stage
COPY --from=builder /app/webhook /app/webhook

# Set execute permissions explicitly
RUN chmod +x /app/webhook

# Create non-root user for security
RUN addgroup -g 1001 webhook && \
    adduser -D -s /bin/sh -u 1001 -G webhook webhook && \
    chown webhook:webhook /app/webhook

# Switch to non-root user
USER webhook

# Expose port
EXPOSE 8443

# Run the webhook
CMD ["/app/webhook"]
