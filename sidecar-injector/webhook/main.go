package main

import (
	"crypto/tls"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"net/http"

	admissionv1 "k8s.io/api/admission/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/serializer"
	"k8s.io/klog/v2"
)

var (
	scheme = runtime.NewScheme()
	codecs = serializer.NewCodecFactory(scheme)
)

type WebhookServer struct {
	server *http.Server
}

type patchOperation struct {
	Op    string      `json:"op"`
	Path  string      `json:"path"`
	Value interface{} `json:"value,omitempty"`
}

func main() {
	certPath := "/etc/certs/tls.crt"
	keyPath := "/etc/certs/tls.key"
	port := "8443"

	cert, err := tls.LoadX509KeyPair(certPath, keyPath)
	if err != nil {
		klog.Fatalf("Failed to load key pair: %v", err)
	}

	server := &WebhookServer{
		server: &http.Server{
			Addr:      fmt.Sprintf(":%s", port),
			TLSConfig: &tls.Config{Certificates: []tls.Certificate{cert}},
		},
	}

	mux := http.NewServeMux()
	mux.HandleFunc("/mutate", server.mutate)
	mux.HandleFunc("/health", server.health)
	server.server.Handler = mux

	klog.Info("Starting webhook server...")
	if err := server.server.ListenAndServeTLS("", ""); err != nil {
		klog.Fatalf("Failed to start webhook server: %v", err)
	}
}

func (ws *WebhookServer) health(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("OK"))
}

func (ws *WebhookServer) mutate(w http.ResponseWriter, r *http.Request) {
	var body []byte
	if r.Body != nil {
		if data, err := ioutil.ReadAll(r.Body); err == nil {
			body = data
		}
	}

	var admissionResponse *admissionv1.AdmissionResponse
	ar := admissionv1.AdmissionReview{}
	if _, _, err := codecs.UniversalDeserializer().Decode(body, nil, &ar); err != nil {
		klog.Errorf("Could not decode body: %v", err)
		admissionResponse = &admissionv1.AdmissionResponse{
			Result: &metav1.Status{
				Message: err.Error(),
			},
		}
	} else {
		admissionResponse = ws.mutateHandler(&ar)
	}

	admissionReview := admissionv1.AdmissionReview{}
	if admissionResponse != nil {
		admissionReview.Response = admissionResponse
		if ar.Request != nil {
			admissionReview.Response.UID = ar.Request.UID
		}
	}

	respBytes, _ := json.Marshal(admissionReview)
	w.Header().Set("Content-Type", "application/json")
	w.Write(respBytes)
}

func (ws *WebhookServer) mutateHandler(ar *admissionv1.AdmissionReview) *admissionv1.AdmissionResponse {
	req := ar.Request
	var pod corev1.Pod
	if err := json.Unmarshal(req.Object.Raw, &pod); err != nil {
		klog.Errorf("Could not unmarshal raw object: %v", err)
		return &admissionv1.AdmissionResponse{
			Result: &metav1.Status{
				Message: err.Error(),
			},
		}
	}

	// Check if sidecar injection is enabled
	if !shouldInject(&pod) {
		return &admissionv1.AdmissionResponse{
			Allowed: true,
		}
	}

	klog.Infof("Injecting Envoy sidecar into pod %s/%s", pod.Namespace, pod.Name)

	patches := createEnvoyPatches(&pod)
	patchBytes, _ := json.Marshal(patches)

	return &admissionv1.AdmissionResponse{
		Allowed: true,
		Patch:   patchBytes,
		PatchType: func() *admissionv1.PatchType {
			pt := admissionv1.PatchTypeJSONPatch
			return &pt
		}(),
	}
}

func shouldInject(pod *corev1.Pod) bool {
	// Check for injection annotation
	if annotations := pod.GetAnnotations(); annotations != nil {
		if inject, exists := annotations["sidecar.envoy.io/inject"]; exists {
			return inject == "true"
		}
	}

	// Check for injection label on namespace or pod
	if labels := pod.GetLabels(); labels != nil {
		if inject, exists := labels["sidecar.envoy.io/inject"]; exists {
			return inject == "true"
		}
	}

	return false
}

func createEnvoyPatches(pod *corev1.Pod) []patchOperation {
	var patches []patchOperation

	// Add Envoy sidecar container
	envoyContainer := corev1.Container{
		Name:  "envoy-sidecar",
		Image: "envoyproxy/envoy:v1.28-latest",
		Ports: []corev1.ContainerPort{
			{
				ContainerPort: 15000,
				Name:          "admin",
			},
			{
				ContainerPort: 15001,
				Name:          "outbound",
			},
			{
				ContainerPort: 15006,
				Name:          "inbound",
			},
		},
		VolumeMounts: []corev1.VolumeMount{
			{
				Name:      "envoy-config",
				MountPath: "/etc/envoy",
			},
		},
		Resources: corev1.ResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("100m"),
				corev1.ResourceMemory: resource.MustParse("128Mi"),
			},
			Limits: corev1.ResourceList{
				corev1.ResourceCPU:    resource.MustParse("200m"),
				corev1.ResourceMemory: resource.MustParse("256Mi"),
			},
		},
		SecurityContext: &corev1.SecurityContext{
			RunAsUser:                &[]int64{1337}[0],
			RunAsGroup:               &[]int64{1337}[0],
			AllowPrivilegeEscalation: &[]bool{false}[0],
		},
	}

	// Add container to pod
	patches = append(patches, patchOperation{
		Op:    "add",
		Path:  "/spec/containers/-",
		Value: envoyContainer,
	})

	// Add volumes
	volumes := []corev1.Volume{
		{
			Name: "envoy-config",
			VolumeSource: corev1.VolumeSource{
				ConfigMap: &corev1.ConfigMapVolumeSource{
					LocalObjectReference: corev1.LocalObjectReference{
						Name: "envoy-config",
					},
				},
			},
		},
	}

	if len(pod.Spec.Volumes) == 0 {
		patches = append(patches, patchOperation{
			Op:    "add",
			Path:  "/spec/volumes",
			Value: volumes,
		})
	} else {
		for _, volume := range volumes {
			patches = append(patches, patchOperation{
				Op:    "add",
				Path:  "/spec/volumes/-",
				Value: volume,
			})
		}
	}

	// Add init container to setup iptables rules
	initContainer := corev1.Container{
		Name:  "envoy-init",
		Image: "envoyproxy/envoy:v1.28-latest",
		Command: []string{
			"/bin/sh",
			"-c",
			`
			# Redirect all outbound traffic to Envoy
			iptables -t nat -A OUTPUT -p tcp --dport 1:65535 -j REDIRECT --to-port 15001
			# Redirect all inbound traffic to Envoy  
			iptables -t nat -A PREROUTING -p tcp --dport 1:65535 -j REDIRECT --to-port 15006
			# Exclude Envoy ports from redirection
			iptables -t nat -I OUTPUT -p tcp --dport 15000:15010 -j ACCEPT
			`,
		},
		SecurityContext: &corev1.SecurityContext{
			Capabilities: &corev1.Capabilities{
				Add: []corev1.Capability{"NET_ADMIN"},
			},
		},
	}

	if len(pod.Spec.InitContainers) == 0 {
		patches = append(patches, patchOperation{
			Op:    "add",
			Path:  "/spec/initContainers",
			Value: []corev1.Container{initContainer},
		})
	} else {
		patches = append(patches, patchOperation{
			Op:    "add",
			Path:  "/spec/initContainers/-",
			Value: initContainer,
		})
	}

	return patches
}
